"""
风电预测API客户端
通过HTTP API请求进行风电出力预测
"""

import argparse
import json
import time
import pandas as pd
import requests
from pathlib import Path
from datetime import datetime
import sys

class WindPowerAPIClient:
    """风电预测API客户端"""
    
    def __init__(self, api_url):
        """初始化API客户端"""
        self.api_url = api_url.rstrip('/')
        self.predict_url = f"{self.api_url}/predict"
        self.health_url = f"{self.api_url}/health"
        self.info_url = f"{self.api_url}/info"
        
    def check_service_health(self):
        """检查API服务健康状态"""
        try:
            print("正在检查API服务状态...")
            response = requests.get(self.health_url, timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API服务正常运行")
                print(f"   服务状态: {health_data.get('status', 'unknown')}")
                print(f"   已加载站点: {', '.join(health_data.get('loaded_sites', []))}")
                return True
            else:
                print(f"❌ API服务异常，状态码: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 无法连接到API服务: {self.api_url}")
            return False
        except Exception as e:
            print(f"❌ 健康检查失败: {str(e)}")
            return False
    
    def get_service_info(self):
        """获取API服务信息"""
        try:
            response = requests.get(self.info_url, timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"获取服务信息失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取服务信息失败: {str(e)}")
            return None
    
    def load_input_data(self, input_file):
        """加载输入数据"""
        input_path = Path(input_file)
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        print(f"正在加载输入数据: {input_file}")
        
        # 尝试不同编码读取CSV文件
        encodings = ['utf-8', 'gbk', 'gb2312']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(input_path, encoding=encoding)
                print(f"✅ 使用 {encoding} 编码成功读取数据")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取失败: {str(e)}")
                continue
        
        if df is None:
            raise ValueError(f"无法读取输入文件，尝试了编码: {encodings}")
        
        print(f"数据加载完成:")
        print(f"  数据量: {len(df)}条记录")
        print(f"  列数: {len(df.columns)}列")
        print(f"  站点: {sorted(df['站点编号'].unique()) if '站点编号' in df.columns else '未知'}")
        
        if '时间' in df.columns:
            print(f"  时间范围: {df['时间'].min()} ~ {df['时间'].max()}")
        
        return df
    
    def send_prediction_request(self, input_df):
        """发送预测请求"""
        print("\n正在发送预测请求...")
        
        # 将DataFrame转换为JSON格式
        csv_data = input_df.to_dict('records')
        
        request_data = {
            'csv_data': csv_data
        }
        
        try:
            start_time = time.time()
            
            # 发送POST请求
            response = requests.post(
                self.predict_url,
                json=request_data,
                timeout=300,  # 5分钟超时
                headers={'Content-Type': 'application/json'}
            )
            
            request_duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success', False):
                    print(f"✅ 预测请求成功完成，耗时: {request_duration:.2f}秒")
                    return result
                else:
                    error_msg = result.get('error', '未知错误')
                    print(f"❌ 预测失败: {error_msg}")
                    return None
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"错误信息: {error_info.get('error', '未知错误')}")
                except:
                    print(f"响应内容: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时，预测可能需要更长时间")
            return None
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接错误，无法访问API服务: {self.api_url}")
            return None
        except Exception as e:
            print(f"❌ 请求发送失败: {str(e)}")
            return None
    
    def save_prediction_result(self, result_data, output_file):
        """保存预测结果"""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        print(f"\n正在保存预测结果到: {output_file}")
        
        # 从API响应中提取预测数据
        predictions = result_data.get('data', [])
        
        if not predictions:
            raise ValueError("API响应中没有预测数据")
        
        # 转换为DataFrame并保存
        predictions_df = pd.DataFrame(predictions)
        predictions_df.to_csv(output_path, index=False, encoding='utf-8')
        
        print(f"✅ 预测结果保存成功")
        print(f"  输出文件: {output_file}")
        print(f"  记录数: {len(predictions_df)}")
        
        # 显示统计信息
        if 'statistics' in result_data:
            stats = result_data['statistics']
            print(f"\n预测统计信息:")
            print(f"  总记录数: {stats.get('total_records', 0)}")
            print(f"  预测站点数: {stats.get('sites_predicted', 0)}")
            print(f"  服务端处理时间: {stats.get('total_duration', 0):.2f}秒")
            
            if 'site_stats' in stats:
                print(f"\n各站点预测结果:")
                for site_id, site_stats in stats['site_stats'].items():
                    print(f"  {site_id.upper()}站点:")
                    print(f"    记录数: {site_stats.get('records', 0)}")
                    print(f"    平均出力: {site_stats.get('mean_power', 0):.2f} MW")
                    print(f"    最大出力: {site_stats.get('max_power', 0):.2f} MW")
                    print(f"    处理时间: {site_stats.get('duration', 0):.2f}秒")
        
        return predictions_df

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='风电预测API客户端')
    parser.add_argument('--input', required=True, help='输入CSV文件路径')
    parser.add_argument('--output', required=True, help='输出CSV文件路径')
    parser.add_argument('--api-url', default='http://***********:8888', help='API服务地址 (默认: http://localhost:8888)')
    parser.add_argument('--check-only', action='store_true', help='仅检查API服务状态，不执行预测')
    
    args = parser.parse_args()
    
    print("="*60)
    print("风电预测API客户端")
    print("="*60)
    
    # 创建API客户端
    client = WindPowerAPIClient(args.api_url)
    
    # 检查API服务状态
    if not client.check_service_health():
        print("\n请确保API服务正在运行:")
        print(f"  python api服务.py")
        sys.exit(1)
    
    # 如果只是检查状态，则退出
    if args.check_only:
        info = client.get_service_info()
        if info:
            print(f"\nAPI服务信息:")
            print(f"  服务名称: {info.get('service', 'unknown')}")
            print(f"  版本: {info.get('version', 'unknown')}")
            print(f"  描述: {info.get('description', 'unknown')}")
            if 'sites' in info:
                print(f"  支持的站点:")
                for site_id, site_info in info['sites'].items():
                    print(f"    {site_id}: {site_info.get('name', 'unknown')} ({site_info.get('capacity', 0)}MW)")
        return
    
    try:
        # 加载输入数据
        input_df = client.load_input_data(args.input)
        
        # 发送预测请求
        result = client.send_prediction_request(input_df)
        
        if result:
            # 保存预测结果
            client.save_prediction_result(result, args.output)
            print(f"\n🎉 预测完成！结果已保存到: {args.output}")
        else:
            print(f"\n❌ 预测失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
