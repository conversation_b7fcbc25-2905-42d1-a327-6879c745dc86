"""
f5风电场配置文件
48MWsmall风电场配置
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.common_constants import *

# F5站点基本信息
F5_CONFIG = {
    'site_info': {
        'site_id': 'f5',
        'name': 'f5风电场',
        'capacity_mw': 48,
        'type': 'small'
    },
    
    # 数据路径配置
    'data_paths': {
        'train_data': DATA_PATHS['train'],
        'test_data': DATA_PATHS['test'],
        'model_dir': PATHS['models'] / 'models_f5',
        'output_dir': PATHS['submissions'],
        'log_dir': PATHS['logs']
    },
    
    # 特征工程配置（small站点特定）
    'feature_config': {
        'time_features': TIME_FEATURES['extended'],
        'lag_features': [1, 2, 3, 6, 12],
        'rolling_windows': [3, 6, 12],
        'weather_features': WEATHER_FEATURES,
        
        # small站点特定特征
        'wind_sensitivity_features': {
            'wind_change_windows': [1, 3, 6] if 'small' == 'small' else [3, 6, 12],
            'volatility_windows': [3, 6, 12] if 'small' == 'small' else [6, 12, 24],
            'acceleration_windows': [1, 2, 3] if 'small' == 'small' else [2, 3, 6]
        },
        
        # 特征选择配置
        'feature_selection': {
            'correlation_threshold': 0.95,
            'importance_threshold': 0.001,
            'max_features': 80
        }
    },
    
    # 模型配置（small站点优化）
    'model_config': {
        'lgb_base_params': {
            **MODEL_CONFIG['lgb_base_params'],
            'max_depth': 6 if 'small' == 'small' else 8,
            'min_data_in_leaf': 20 if 'small' == 'small' else 50,
            'lambda_l1': 0.1,
            'lambda_l2': 0.1
        },
        
        'optuna_config': {
            'n_trials': 50,
            'timeout': 1800 if 'small' == 'small' else 3600,
            'cv_folds': 5,
            'scoring': 'neg_root_mean_squared_error'
        },
        
        'hyperparameter_space': HYPERPARAMETER_SPACE['small_site']
    },
    
    # 数据处理配置
    'data_processing': {
        **DATA_PROCESSING_CONFIG,
        'outlier_quantiles': (0.01, 0.99) if 'small' == 'small' else (0.005, 0.995),
        'missing_strategy': 'interpolate',
        'smoothing_window': 3 if 'small' == 'small' else 5
    },
    
    # 预测配置
    'prediction_config': {
        'output_columns': OUTPUT_CONFIG['columns'],
        'time_format': OUTPUT_CONFIG['time_format'],
        'capacity_constraint': True,
        'min_output': 0.0,
        'max_output': 48.0,
        'post_processing': {
            'smooth_predictions': True,
            'smooth_window': 3 if 'small' == 'small' else 5
        }
    },
    
    # 错误处理配置
    'error_handling': {
        **ERROR_HANDLING_CONFIG,
        'site_specific_fallbacks': {
            'data_missing_threshold': 0.05,
            'model_performance_threshold': 0.8,
            'prediction_range_check': True
        }
    },
    
    # 日志配置
    'logging': {
        **LOGGING_CONFIG,
        'log_file': f'logs/f5_site.log',
        'detailed_logging': True
    },
    
    # 验证配置
    'validation': {
        'train_test_split': 0.2,
        'time_series_split': True,
        'validation_metrics': EVALUATION_METRICS,
        'early_stopping_rounds': 100
    }
}

# 导出配置
def get_f5_config():
    """获取F5站点配置"""
    # 确保模型目录存在
    F5_CONFIG['data_paths']['model_dir'].mkdir(exist_ok=True)
    return F5_CONFIG

if __name__ == "__main__":
    # 测试配置
    config = get_f5_config()
    print("F5站点配置加载成功")
    print(f"站点ID: {config['site_info']['site_id']}")
    print(f"装机容量: {config['site_info']['capacity_mw']}MW")
    print(f"模型目录: {config['data_paths']['model_dir']}")
