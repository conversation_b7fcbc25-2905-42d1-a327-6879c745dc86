"""
测试API返回格式是否符合规范
"""

import requests
import pandas as pd
import json
from pathlib import Path

def test_success_response():
    """测试成功响应格式"""
    print("=== 测试成功响应格式 ===")
    
    # 创建测试数据
    test_file = "temp_test.csv"
    
    # 读取原始测试数据的前10行
    original_file = Path("data/test/测试集A_风电预测_气象变量.csv")
    if original_file.exists():
        df = pd.read_csv(original_file)
        test_df = df.head(10)  # 只取10行进行快速测试
        test_df.to_csv(test_file, index=False)
    else:
        print("原始测试文件不存在，跳过测试")
        return
    
    # 发送请求
    url = "http://localhost:8888/predict"
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test.csv', f, 'text/csv')}
            response = requests.post(url, files=files)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 验证响应格式
            print("\n=== 格式验证 ===")
            
            # 检查必需字段
            if 'status' in result:
                print(f"✅ status字段存在: {result['status']}")
                if result['status'] == 1:
                    print("✅ status值为1（成功）")
                else:
                    print(f"❌ status值不是1: {result['status']}")
            else:
                print("❌ 缺少status字段")
            
            if 'results' in result:
                print(f"✅ results字段存在")
                results = result['results']
                
                if isinstance(results, list):
                    print(f"✅ results是列表，包含{len(results)}个站点")
                    
                    for i, site_result in enumerate(results):
                        print(f"\n站点 {i+1}:")
                        
                        if 'site' in site_result:
                            print(f"  ✅ site字段存在: {site_result['site']}")
                        else:
                            print(f"  ❌ 缺少site字段")
                        
                        if 'power' in site_result:
                            power = site_result['power']
                            if isinstance(power, list):
                                print(f"  ✅ power字段是列表，包含{len(power)}个值")
                                print(f"  前5个值: {power[:5]}")
                            else:
                                print(f"  ❌ power字段不是列表: {type(power)}")
                        else:
                            print(f"  ❌ 缺少power字段")
                else:
                    print(f"❌ results不是列表: {type(results)}")
            else:
                print("❌ 缺少results字段")
        else:
            print(f"请求失败: {response.text}")
    
    except Exception as e:
        print(f"测试失败: {str(e)}")
    
    finally:
        # 清理临时文件
        if Path(test_file).exists():
            Path(test_file).unlink()

def test_failure_response():
    """测试失败响应格式"""
    print("\n=== 测试失败响应格式 ===")
    
    # 发送无效请求（没有文件）
    url = "http://localhost:8888/predict"
    
    try:
        response = requests.post(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code != 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 验证失败响应格式
            print("\n=== 失败格式验证 ===")
            
            if 'status' in result:
                print(f"✅ status字段存在: {result['status']}")
                if result['status'] == 0:
                    print("✅ status值为0（失败）")
                else:
                    print(f"❌ status值不是0: {result['status']}")
            else:
                print("❌ 缺少status字段")
            
            if 'results' in result:
                results = result['results']
                if isinstance(results, list) and len(results) == 0:
                    print("✅ results是空列表")
                else:
                    print(f"❌ results不是空列表: {results}")
            else:
                print("❌ 缺少results字段")
    
    except Exception as e:
        print(f"测试失败: {str(e)}")

def test_invalid_file():
    """测试无效文件响应格式"""
    print("\n=== 测试无效文件响应格式 ===")
    
    # 创建无效的CSV文件
    invalid_file = "invalid_test.csv"
    with open(invalid_file, 'w') as f:
        f.write("invalid,csv,content\n1,2")  # 不完整的CSV
    
    url = "http://localhost:8888/predict"
    
    try:
        with open(invalid_file, 'rb') as f:
            files = {'file': ('invalid.csv', f, 'text/csv')}
            response = requests.post(url, files=files)
        
        print(f"状态码: {response.status_code}")
        
        result = response.json()
        print("响应内容:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 验证格式
        print("\n=== 无效文件格式验证 ===")
        
        if result.get('status') == 0 and result.get('results') == []:
            print("✅ 无效文件返回正确的失败格式")
        else:
            print("❌ 无效文件返回格式不正确")
    
    except Exception as e:
        print(f"测试失败: {str(e)}")
    
    finally:
        # 清理临时文件
        if Path(invalid_file).exists():
            Path(invalid_file).unlink()

def test_health_check():
    """测试健康检查接口"""
    print("\n=== 测试健康检查接口 ===")
    
    try:
        response = requests.get("http://localhost:8888/health")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("健康检查响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"健康检查失败: {response.text}")
    
    except Exception as e:
        print(f"健康检查失败: {str(e)}")

if __name__ == "__main__":
    print("API格式测试工具")
    print("确保API服务正在运行: python api服务.py")
    print("="*50)
    
    test_health_check()
    test_success_response()
    test_failure_response()
    test_invalid_file()
    
    print("\n=== 测试完成 ===")
