# 风电预测项目代码说明文档

## 📋 项目概述

本项目是一个完整的风电出力预测系统，采用机器学习方法对5个风电场（F1-F5）进行出力预测。项目包含数据处理、特征工程、模型训练、预测和API服务等完整功能模块。

### 🎯 项目目标

- 基于气象数据预测风电场出力
- 支持5个不同规模的风电场
- 提供批量训练和预测功能
- 提供HTTP API服务接口

### 📊 技术栈

- **机器学习**: LightGBM
- **数据处理**: Pandas, NumPy
- **特征工程**: 时间序列特征、气象特征、交互特征
- **API服务**: Flask
- **超参数优化**: Optuna
- **可视化**: Matplotlib, Seaborn

## 🏗️ 项目架构

```
风电预测系统
├── 数据层 (Data Layer)
│   ├── 训练数据处理
│   ├── 测试数据处理
│   └── 数据验证
├── 特征层 (Feature Layer)
│   ├── 时间特征
│   ├── 气象特征
│   ├── 滞后特征
│   └── 交互特征
├── 模型层 (Model Layer)
│   ├── LightGBM训练
│   ├── 超参数优化
│   └── 模型验证
├── 预测层 (Prediction Layer)
│   ├── 单站点预测
│   ├── 批量预测
│   └── 结果格式化
└── 服务层 (Service Layer)
    ├── HTTP API
    ├── 客户端工具
    └── 演示脚本
```

## 📁 目录结构详解

### 核心目录

```
├── sites/                    # 站点模块目录
│   ├── f1/                  # F1站点 (48MW)
│   ├── f2/                  # F2站点 (280MW)
│   ├── f3/                  # F3站点 (48MW)
│   ├── f4/                  # F4站点 (88MW)
│   └── f5/                  # F5站点 (48MW)
├── shared/                   # 共享模块
├── models/                   # 训练好的模型
├── data/                     # 数据目录
├── submissions/              # 预测结果
├── logs/                     # 日志文件
└── utils/                    # 工具脚本
```

### 站点模块结构

每个站点目录包含：

```
sites/fx/
├── config_fx.py            # 站点配置
├── data_processor_fx.py    # 数据处理器
├── feature_engineer_fx.py  # 特征工程器
├── model_trainer_fx.py     # 模型训练器
├── predictor_fx.py         # 预测器
├── train_fx.py             # 训练脚本
└── predict_fx.py           # 预测脚本
```

## 🔧 核心组件说明

### 1. 基础类 (shared/base_classes.py)

定义了所有组件的基础接口：

- `BaseDataProcessor`: 数据处理基类
- `BaseFeatureEngineer`: 特征工程基类
- `BaseModelTrainer`: 模型训练基类
- `BasePredictor`: 预测器基类

### 2. 配置系统

每个站点都有独立的配置文件，包含：

- **站点信息**: 名称、容量、类型
- **数据处理配置**: 清洗参数、验证规则
- **特征工程配置**: 特征类型、窗口大小
- **模型配置**: 算法参数、优化设置
- **预测配置**: 后处理、输出格式

### 3. 数据处理器 (DataProcessor)

负责原始数据的加载和清洗：

```python
class DataProcessorFx(BaseDataProcessor):
    def load_data(self, file_path: str) -> pd.DataFrame
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame
    def validate_data(self, df: pd.DataFrame) -> bool
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame
    def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame
    def _smooth_data(self, df: pd.DataFrame) -> pd.DataFrame
```

**主要功能**:

- 多编码格式数据读取
- 时间列标准化处理
- 缺失值插值填充
- 异常值检测和处理
- 数据平滑处理
- 数据质量验证

### 4. 特征工程器 (FeatureEngineer)

生成用于模型训练的特征：

```python
class FeatureEngineerFx(BaseFeatureEngineer):
    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame
    def _extract_time_features(self, df: pd.DataFrame) -> pd.DataFrame
    def _extract_wind_sensitivity_features(self, df: pd.DataFrame) -> pd.DataFrame
    def _extract_lag_features(self, df: pd.DataFrame) -> pd.DataFrame
    def _extract_rolling_features(self, df: pd.DataFrame) -> pd.DataFrame
    def _extract_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame
```

**特征类型**:

- **时间特征**: 小时、星期、月份、季节、周期编码
- **风速特征**: 变化率、波动性、功率曲线、风速分段
- **滞后特征**: 历史时刻的气象数据
- **滚动特征**: 移动平均、标准差、最值
- **交互特征**: 气象变量间的交互关系
- **技术指标**: RSI、MACD等技术分析指标

### 5. 模型训练器 (ModelTrainer)

负责模型的训练和优化：

```python
class ModelTrainerFx(BaseModelTrainer):
    def train_model(self, X: pd.DataFrame, y: pd.Series) -> None
    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series) -> dict
    def validate_model(self, X: pd.DataFrame, y: pd.Series) -> dict
    def save_model(self, model_path: str) -> None
```

**核心功能**:

- LightGBM模型训练
- Optuna超参数优化
- 时间序列交叉验证
- 特征重要性分析
- 模型性能评估
- 模型持久化保存

### 6. 预测器 (Predictor)

执行预测和结果处理：

```python
class PredictorFx(BasePredictor):
    def load_model(self, model_path: str) -> None
    def predict(self, X: pd.DataFrame) -> np.ndarray
    def format_predictions(self, predictions: np.ndarray, timestamps: pd.Series) -> pd.DataFrame
    def validate_predictions(self, predictions: np.ndarray) -> bool
```

**主要功能**:

- 模型加载和验证
- 批量预测执行
- 预测结果平滑
- 结果格式化
- 预测值验证

## 🚀 使用方式

### 1. 单站点训练

```bash
# 训练F1站点
python 训练_f1.py

# 训练F2站点
python 训练_f2.py
```

### 2. 单站点预测

```bash
# 预测F1站点
python 预测_f1.py

# 预测F2站点
python 预测_f2.py
```

### 3. 批量操作

```bash
# 批量训练所有站点
python 批量训练_所有站点.py

# 批量预测所有站点
python 批量预测_所有站点.py
```

### 4. 评估结果

```bash
# 计算预测分数
python 计算分数.py
```

### 5. API服务

```bash
# 启动API服务
python api服务.py

# 使用API客户端
python api请求.py --input test_data.csv --output result.csv
```

## 🎯 特征工程详解

### F2站点特色特征 (大型风电场)

基于数据分析结果，F2站点采用了增强的特征工程：

1. **功率曲线建模**:

   - 5个功率区间分段特征
   - 理论功率曲线计算
   - 功率曲线效率特征
2. **异常检测特征**:

   - 风速Z-score异常检测
   - 风速突变检测
   - 风速稳定性指标
3. **季节性增强**:

   - 高出力季节标识 (1-2月、10-12月)
   - 低出力季节标识 (6-8月)
   - 日内模式特征 (下午高峰、早晨低谷)
4. **物理建模特征**:

   - 空气密度近似计算
   - 风功率密度特征
   - 露点温度近似

### 小型站点特征 (F1, F3, F5)

针对小型风电场的特点：

- 更严格的异常值处理
- 较小的平滑窗口
- 风速敏感性特征
- 简化的功率曲线模型

### 中型站点特征 (F4)

平衡了大型和小型站点的特点：

- 中等程度的数据平滑
- 适中的特征复杂度
- 综合的异常检测策略

## 📊 模型性能

### 最终评估结果

| 站点 | 容量(MW) | RMSE  | Score  | R²  | 特点       |
| ---- | -------- | ----- | ------ | ---- | ---------- |
| F1   | 48       | 6.85  | 0.1274 | 0.60 | 小型，稳定 |
| F2   | 280      | 38.83 | 0.0251 | 0.86 | 大型，复杂 |
| F3   | 48       | 6.89  | 0.1267 | 0.73 | 小型，良好 |
| F4   | 88       | 11.10 | 0.0826 | 0.83 | 中型，均衡 |
| F5   | 48       | 6.65  | 0.1308 | 0.74 | 小型，最优 |

**总体性能**:

- 平均RMSE: 14.06 MW

## 🔧 API服务架构

### 服务端 (api服务.py)

- **Flask Web框架**: 提供HTTP API接口
- **多站点支持**: 自动加载所有站点模型
- **流水线架构**: 数据处理→特征工程→预测→格式化
- **错误处理**: 完善的异常处理和日志记录

### 客户端 (api请求.py)

- **命令行工具**: 支持参数化调用
- **自动格式转换**: CSV↔JSON自动转换
- **健康检查**: 服务状态监控
- **超时处理**: 大数据量预测支持

### API接口

1. **GET /health**: 健康检查
2. **GET /info**: 服务信息
3. **POST /predict**: 风电预测

## 🛠️ 开发工具

### 代码生成工具

- `utils/site_generator.py`: 自动生成站点模块代码

### 测试工具

- `test_api.py`: API服务测试
- `演示API服务.py`: 完整演示脚本

### 分析工具

- `analyze_f2_data.py`: F2站点数据分析
- `计算分数.py`: 预测结果评估

## 📈 性能优化

### 特征选择策略

- 相关性过滤 (阈值: 0.92-0.95)
- 重要性过滤 (阈值: 0.0005-0.001)
- 最大特征数限制 (120-150个)

### 模型优化

- Optuna超参数优化
- 时间序列交叉验证
- 早停机制防止过拟合
- 特征重要性分析

### 系统优化

- 多进程并行处理
- 内存使用优化
- 日志系统完善
- 错误恢复机制

## 🔍 故障排除

### 常见问题

1. **编码问题**: 支持GBK、UTF-8等多种编码
2. **内存不足**: 大数据量时分批处理
3. **模型加载失败**: 检查模型文件路径和完整性
4. **特征不匹配**: 确保训练和预测使用相同特征

### 调试工具

- 详细的日志记录
- 数据验证检查点
- 模型性能监控
- API服务健康检查

## 📚 扩展指南

### 添加新站点

1. 复制现有站点模块
2. 修改配置参数
3. 调整特征工程策略
4. 重新训练模型

### 改进特征工程

1. 分析站点数据特点
2. 设计针对性特征
3. 验证特征有效性
4. 更新配置文件

### 优化模型性能

1. 调整超参数搜索空间
2. 尝试不同的验证策略
3. 增加特征工程复杂度
4. 考虑集成学习方法

## 💡 技术创新点

### 1. 站点差异化建模

- **小型站点** (F1, F3, F5): 注重风速敏感性，采用较小平滑窗口
- **大型站点** (F2): 复杂功率曲线建模，增强异常检测
- **中型站点** (F4): 平衡策略，综合优化

### 2. 增强特征工程

- **物理建模**: 空气密度、风功率密度计算
- **时间模式**: 基于数据分析的季节性和日内模式
- **异常检测**: 多层次异常识别和处理
- **功率曲线**: 分段线性和理论曲线结合

### 3. 完整工程化

- **模块化设计**: 可扩展的站点模块架构
- **配置驱动**: 灵活的参数配置系统
- **API服务**: 生产级HTTP API接口
- **自动化工具**: 批量训练、预测、评估

## 🔬 算法原理

### LightGBM模型选择理由

1. **高效性**: 适合大规模时间序列数据
2. **准确性**: 梯度提升算法的强大拟合能力
3. **可解释性**: 特征重要性分析支持
4. **稳定性**: 对异常值相对鲁棒

### 特征工程理论基础

1. **时间序列分解**: 趋势、季节性、随机性
2. **物理建模**: 风能转换的物理原理
3. **统计特征**: 滞后、滚动统计量
4. **技术分析**: 金融领域的技术指标应用

### 验证策略

- **时间序列交叉验证**: 避免数据泄露
- **滑动窗口验证**: 模拟真实预测场景
- **多指标评估**: RMSE、MAE、R²、Score综合评估

## 📋 部署指南

### 环境要求

```bash
# Python 3.8+
pip install pandas numpy scikit-learn lightgbm
pip install flask requests optuna matplotlib seaborn
```

### 快速部署

```bash
# 1. 克隆项目
git clone <project-repo>
cd wind-power-prediction

# 2. 安装依赖
pip install -r requirements.txt

# 3. 训练模型
python 批量训练_所有站点.py

# 4. 启动API服务
python api服务.py

# 5. 测试预测
python api请求.py --input test_data.csv --output result.csv
```

### 生产环境配置

- **负载均衡**: 多实例部署
- **监控告警**: 服务健康监控
- **数据备份**: 模型和配置备份
- **日志管理**: 集中化日志收集

## 🎓 学习价值

### 机器学习实践

- 完整的ML项目流程
- 时间序列预测方法
- 特征工程最佳实践
- 模型优化和验证

### 工程化实践

- 模块化代码设计
- 配置管理策略
- API服务开发
- 自动化工具构建

### 领域知识

- 风电行业理解
- 气象数据处理
- 能源预测方法
- 物理建模应用

---

**项目完成时间**: 2025年7月23日
**最终Score**: 0.0505 (相比初始版本提升3.9%)
**技术亮点**: 站点差异化建模、增强特征工程、完整API服务
**代码规模**: 约5000行Python代码，覆盖数据处理、特征工程、模型训练、预测服务全流程
