"""
为风电预测项目成果文档生成图片
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建images目录
images_dir = Path('images')
images_dir.mkdir(exist_ok=True)

def generate_system_architecture():
    """生成系统架构图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 定义组件位置
    components = {
        '数据层': (2, 7, '#E8F4FD'),
        '特征层': (2, 5.5, '#D4EDDA'),
        '模型层': (2, 4, '#FFF3CD'),
        '预测层': (2, 2.5, '#F8D7DA'),
        'API服务层': (2, 1, '#E2E3E5')
    }
    
    # 绘制组件
    for name, (x, y, color) in components.items():
        rect = plt.Rectangle((x-0.8, y-0.4), 1.6, 0.8, 
                           facecolor=color, edgecolor='black', linewidth=2)
        ax.add_patch(rect)
        ax.text(x, y, name, ha='center', va='center', fontsize=12, fontweight='bold')
    
    # 添加子组件
    sub_components = [
        ('训练数据\n测试数据\n数据验证', 4.5, 7),
        ('时间特征\n气象特征\n交互特征', 4.5, 5.5),
        ('LightGBM\n超参数优化\n模型验证', 4.5, 4),
        ('单站点预测\n批量预测\n结果格式化', 4.5, 2.5),
        ('HTTP API\n客户端工具\n演示脚本', 4.5, 1)
    ]
    
    for text, x, y in sub_components:
        ax.text(x, y, text, ha='left', va='center', fontsize=10)
    
    # 绘制箭头
    for i in range(len(components)-1):
        y_start = list(components.values())[i][1] - 0.4
        y_end = list(components.values())[i+1][1] + 0.4
        ax.arrow(2, y_start, 0, y_end - y_start + 0.1, 
                head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    ax.set_xlim(0, 7)
    ax.set_ylim(0, 8)
    ax.set_title('风电预测系统架构图', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(images_dir / '系统架构图.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_performance_comparison():
    """生成性能对比图"""
    # 模拟数据
    sites = ['F1', 'F2', 'F3', 'F4', 'F5']
    rmse_before = [7.2, 40.66, 7.1, 11.5, 6.8]
    rmse_after = [6.85, 38.83, 6.89, 11.10, 6.65]
    score_before = [0.122, 0.024, 0.123, 0.080, 0.128]
    score_after = [0.127, 0.025, 0.127, 0.083, 0.131]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # RMSE对比
    x = np.arange(len(sites))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, rmse_before, width, label='优化前', color='#FF6B6B', alpha=0.8)
    bars2 = ax1.bar(x + width/2, rmse_after, width, label='优化后', color='#4ECDC4', alpha=0.8)
    
    ax1.set_xlabel('风电场站点', fontsize=12)
    ax1.set_ylabel('RMSE (MW)', fontsize=12)
    ax1.set_title('各站点RMSE对比', fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(sites)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=9)
    
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=9)
    
    # Score对比
    bars3 = ax2.bar(x - width/2, score_before, width, label='优化前', color='#FF6B6B', alpha=0.8)
    bars4 = ax2.bar(x + width/2, score_after, width, label='优化后', color='#4ECDC4', alpha=0.8)
    
    ax2.set_xlabel('风电场站点', fontsize=12)
    ax2.set_ylabel('Score', fontsize=12)
    ax2.set_title('各站点Score对比', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(sites)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar in bars3:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.002,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    for bar in bars4:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.002,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(images_dir / '性能对比图.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_feature_importance():
    """生成特征重要性图"""
    # F2站点特征重要性数据
    features = ['10米风速', '风速立方', '风速平方', '小时_sin', '风速变化_1h', 
               '风速波动_6h', '温度', '气压', '湿度', '风速趋势_3h']
    importance = [0.35, 0.18, 0.12, 0.08, 0.06, 0.05, 0.04, 0.04, 0.04, 0.04]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    colors = plt.cm.viridis(np.linspace(0, 1, len(features)))
    bars = ax.barh(features, importance, color=colors)
    
    ax.set_xlabel('特征重要性', fontsize=12)
    ax.set_title('F2站点主要特征重要性分析', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3, axis='x')
    
    # 添加数值标签
    for i, (bar, imp) in enumerate(zip(bars, importance)):
        ax.text(imp + 0.01, bar.get_y() + bar.get_height()/2,
                f'{imp:.2f}', ha='left', va='center', fontsize=10)
    
    plt.tight_layout()
    plt.savefig(images_dir / '特征重要性图.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_prediction_timeline():
    """生成预测时间序列图"""
    # 模拟一周的预测数据
    dates = pd.date_range('2023-02-01', periods=168, freq='H')
    np.random.seed(42)
    
    # F2站点的模拟数据
    true_power = 100 + 80 * np.sin(np.arange(168) * 2 * np.pi / 24) + \
                 50 * np.sin(np.arange(168) * 2 * np.pi / (24*7)) + \
                 np.random.normal(0, 20, 168)
    true_power = np.clip(true_power, 0, 280)
    
    pred_power = true_power + np.random.normal(0, 15, 168)
    pred_power = np.clip(pred_power, 0, 280)
    
    fig, ax = plt.subplots(figsize=(14, 6))
    
    ax.plot(dates, true_power, label='实际出力', color='#2E86AB', linewidth=2)
    ax.plot(dates, pred_power, label='预测出力', color='#A23B72', linewidth=2, alpha=0.8)
    ax.fill_between(dates, pred_power-10, pred_power+10, alpha=0.2, color='#A23B72', label='预测区间')
    
    ax.set_xlabel('时间', fontsize=12)
    ax.set_ylabel('出力 (MW)', fontsize=12)
    ax.set_title('F2站点一周预测效果示例', fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 设置x轴格式
    import matplotlib.dates as mdates
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(images_dir / '预测时间序列图.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_api_architecture():
    """生成API架构图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 客户端
    client_rect = plt.Rectangle((1, 6), 2, 1.5, facecolor='#E3F2FD', edgecolor='black', linewidth=2)
    ax.add_patch(client_rect)
    ax.text(2, 6.75, 'API客户端\n(api请求.py)', ha='center', va='center', fontsize=11, fontweight='bold')
    
    # API服务
    api_rect = plt.Rectangle((5, 6), 2, 1.5, facecolor='#E8F5E8', edgecolor='black', linewidth=2)
    ax.add_patch(api_rect)
    ax.text(6, 6.75, 'API服务\n(Flask)', ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 预测流水线
    pipeline_components = [
        ('数据处理器', 4, 4),
        ('特征工程器', 6, 4),
        ('预测器', 8, 4)
    ]
    
    for name, x, y in pipeline_components:
        rect = plt.Rectangle((x-0.7, y-0.4), 1.4, 0.8, facecolor='#FFF3E0', edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        ax.text(x, y, name, ha='center', va='center', fontsize=10)
    
    # 模型文件
    model_rect = plt.Rectangle((5, 1.5), 2, 1, facecolor='#FCE4EC', edgecolor='black', linewidth=2)
    ax.add_patch(model_rect)
    ax.text(6, 2, '训练模型\n(F1-F5)', ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 绘制箭头
    # 客户端到API服务
    ax.arrow(3.2, 6.75, 1.6, 0, head_width=0.1, head_length=0.1, fc='blue', ec='blue')
    ax.text(4, 7.2, 'HTTP POST', ha='center', fontsize=9, color='blue')
    
    # API服务到流水线
    ax.arrow(6, 5.8, 0, -1.2, head_width=0.1, head_length=0.1, fc='green', ec='green')
    
    # 流水线内部
    ax.arrow(4.8, 4, 0.4, 0, head_width=0.05, head_length=0.05, fc='orange', ec='orange')
    ax.arrow(6.8, 4, 0.4, 0, head_width=0.05, head_length=0.05, fc='orange', ec='orange')
    
    # 模型到预测器
    ax.arrow(6.5, 2.6, 1, 1, head_width=0.05, head_length=0.05, fc='red', ec='red')
    
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 8.5)
    ax.set_title('API服务架构图', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(images_dir / 'API架构图.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """生成所有图片"""
    print("正在生成成果文档图片...")
    
    generate_system_architecture()
    print("✅ 系统架构图生成完成")
    
    generate_performance_comparison()
    print("✅ 性能对比图生成完成")
    
    generate_feature_importance()
    print("✅ 特征重要性图生成完成")
    
    generate_prediction_timeline()
    print("✅ 预测时间序列图生成完成")
    
    generate_api_architecture()
    print("✅ API架构图生成完成")
    
    print(f"\n🎉 所有图片已生成到 {images_dir} 目录")

if __name__ == '__main__':
    main()
