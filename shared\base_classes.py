"""
基础抽象类定义
提供最小化的共享接口，确保站点独立性
"""

from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
import logging


class BaseDataProcessor(ABC):
    """基础数据处理器抽象类"""
    
    def __init__(self, site_id: str):
        self.site_id = site_id
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置站点特定的日志器"""
        logger = logging.getLogger(f"DataProcessor_{self.site_id}")
        if not logger.handlers:
            handler = logging.FileHandler(f"logs/{self.site_id}_data_processor.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def load_data(self, file_path: str) -> pd.DataFrame:
        """加载数据"""
        pass
    
    @abstractmethod
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        pass
    
    @abstractmethod
    def validate_data(self, df: pd.DataFrame) -> bool:
        """验证数据质量"""
        pass


class BaseFeatureEngineer(ABC):
    """基础特征工程抽象类"""
    
    def __init__(self, site_id: str):
        self.site_id = site_id
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置站点特定的日志器"""
        logger = logging.getLogger(f"FeatureEngineer_{self.site_id}")
        if not logger.handlers:
            handler = logging.FileHandler(f"logs/{self.site_id}_feature_engineer.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取特征"""
        pass
    
    @abstractmethod
    def select_features(self, df: pd.DataFrame, target: Optional[pd.Series] = None) -> pd.DataFrame:
        """选择特征"""
        pass
    
    @abstractmethod
    def get_feature_names(self) -> list:
        """获取特征名称列表"""
        pass


class BaseModelTrainer(ABC):
    """基础模型训练器抽象类"""
    
    def __init__(self, site_id: str):
        self.site_id = site_id
        self.logger = self._setup_logger()
        self.model = None
        self.best_params = None
    
    def _setup_logger(self) -> logging.Logger:
        """设置站点特定的日志器"""
        logger = logging.getLogger(f"ModelTrainer_{self.site_id}")
        if not logger.handlers:
            handler = logging.FileHandler(f"logs/{self.site_id}_model_trainer.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """超参数优化"""
        pass
    
    @abstractmethod
    def train_model(self, X: pd.DataFrame, y: pd.Series, params: Dict[str, Any]) -> Any:
        """训练模型"""
        pass
    
    @abstractmethod
    def evaluate_model(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """评估模型"""
        pass
    
    @abstractmethod
    def save_model(self, model_path: str) -> None:
        """保存模型"""
        pass


class BasePredictor(ABC):
    """基础预测器抽象类"""
    
    def __init__(self, site_id: str):
        self.site_id = site_id
        self.logger = self._setup_logger()
        self.model = None
    
    def _setup_logger(self) -> logging.Logger:
        """设置站点特定的日志器"""
        logger = logging.getLogger(f"Predictor_{self.site_id}")
        if not logger.handlers:
            handler = logging.FileHandler(f"logs/{self.site_id}_predictor.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def load_model(self, model_path: str) -> None:
        """加载模型"""
        pass
    
    @abstractmethod
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """执行预测"""
        pass
    
    @abstractmethod
    def format_predictions(self, predictions: np.ndarray, timestamps: pd.Series) -> pd.DataFrame:
        """格式化预测结果"""
        pass
    
    @abstractmethod
    def save_predictions(self, predictions_df: pd.DataFrame, output_path: str) -> None:
        """保存预测结果"""
        pass


class BaseErrorHandler(ABC):
    """基础错误处理器抽象类"""
    
    def __init__(self, site_id: str):
        self.site_id = site_id
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置站点特定的日志器"""
        logger = logging.getLogger(f"ErrorHandler_{self.site_id}")
        if not logger.handlers:
            handler = logging.FileHandler(f"logs/{self.site_id}_error_handler.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def handle_data_error(self, error: Exception, context: str) -> Any:
        """处理数据错误"""
        pass
    
    @abstractmethod
    def handle_model_error(self, error: Exception, context: str) -> Any:
        """处理模型错误"""
        pass
    
    @abstractmethod
    def handle_prediction_error(self, error: Exception, context: str) -> Any:
        """处理预测错误"""
        pass
