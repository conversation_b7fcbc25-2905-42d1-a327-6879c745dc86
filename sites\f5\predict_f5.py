"""
F5站点预测主脚本
执行F5站点的完整预测流程
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from sites.f5.config_f5 import get_f5_config
from sites.f5.data_processor_f5 import DataProcessorF5
from sites.f5.feature_engineer_f5 import FeatureEngineerF5
from sites.f5.predictor_f5 import PredictorF5

import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/f5_predict.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('F5_Predict')

def load_feature_names(model_dir: Path) -> list:
    """加载特征名称"""
    feature_names_path = model_dir / 'f5_feature_names.txt'
    if feature_names_path.exists():
        with open(feature_names_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines()]
    return None

def main():
    """F5站点预测主函数"""
    logger = setup_logging()
    start_time = time.time()
    
    try:
        logger.info("="*50)
        logger.info("开始F5站点预测流程")
        logger.info("="*50)
        
        # 1. 加载配置
        config = get_f5_config()
        logger.info(f"站点信息: {config['site_info']['name']} ({config['site_info']['capacity_mw']}MW)")
        
        # 2. 检查模型文件
        model_path = config['data_paths']['model_dir'] / 'f5_lgb_model.pkl'
        if not model_path.exists():
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        logger.info(f"模型文件: {model_path}")
        
        # 3. 数据处理
        logger.info("步骤1: 测试数据加载和处理")
        processor = DataProcessorF5()
        
        # 加载测试数据
        test_data = processor.load_data(str(config['data_paths']['test_data']), is_train=False)
        logger.info(f"测试数据加载完成: {test_data.shape}")
        
        # 数据清洗
        clean_test_data = processor.clean_data(test_data)
        logger.info(f"测试数据清洗完成: {clean_test_data.shape}")
        
        # 数据验证
        if not processor.validate_data(clean_test_data):
            logger.warning("测试数据验证未通过，但继续执行预测")
        else:
            logger.info("测试数据验证通过")
        
        # 4. 特征工程
        logger.info("步骤2: 特征工程")
        engineer = FeatureEngineerF5()
        
        # 特征提取
        features_data = engineer.extract_features(clean_test_data)
        logger.info(f"特征提取完成: {features_data.shape}")
        
        # 加载训练时的特征名称
        feature_names = load_feature_names(config['data_paths']['model_dir'])
        if feature_names is None:
            logger.warning("未找到保存的特征名称，使用当前提取的特征")
            feature_names = engineer.get_feature_names()
        else:
            logger.info(f"加载训练时特征名称: {len(feature_names)}个")
        
        # 确保特征一致性
        available_features = [col for col in feature_names if col in features_data.columns]
        missing_features = set(feature_names) - set(available_features)
        
        if missing_features:
            logger.warning(f"缺少特征: {missing_features}")
            # 为缺少的特征填充0
            for feature in missing_features:
                features_data[feature] = 0
            logger.info("已为缺少的特征填充0值")
        
        # 准备预测数据
        X_test = features_data[feature_names].fillna(0)
        logger.info(f"预测数据准备完成: {X_test.shape}")
        
        # 5. 模型预测
        logger.info("步骤3: 模型预测")
        predictor = PredictorF5()
        
        # 加载模型
        predictor.load_model(str(model_path))
        predictor.set_feature_names(feature_names)
        
        # 执行预测
        predictions = predictor.predict(X_test)
        logger.info(f"预测完成，预测值范围: [{np.min(predictions):.2f}, {np.max(predictions):.2f}]")
        
        # 验证预测结果
        if not predictor.validate_predictions(predictions):
            logger.warning("预测结果验证未通过，但继续保存结果")
        
        # 6. 格式化和保存结果
        logger.info("步骤4: 格式化和保存预测结果")
        
        # 格式化预测结果
        result_df = predictor.format_predictions(predictions, clean_test_data['时间'])
        logger.info(f"预测结果格式化完成: {result_df.shape}")
        
        # 显示预测统计信息
        logger.info("预测统计信息:")
        logger.info(f"  平均出力: {np.mean(predictions):.2f} MW")
        logger.info(f"  最大出力: {np.max(predictions):.2f} MW")
        logger.info(f"  最小出力: {np.min(predictions):.2f} MW")
        logger.info(f"  标准差: {np.std(predictions):.2f} MW")
        logger.info(f"  容量因子: {np.mean(predictions)/config['site_info']['capacity_mw']*100:.1f}%")
        
        # 保存预测结果
        output_path = config['data_paths']['output_dir'] / 'f5_predictions.csv'
        predictor.save_predictions(result_df, str(output_path))
        logger.info(f"预测结果保存: {output_path}")
        
        # 7. 生成预测报告
        logger.info("步骤5: 生成预测报告")
        report_path = config['data_paths']['output_dir'] / 'f5_prediction_report.txt'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("F5站点预测报告\n")
            f.write("="*30 + "\n")
            f.write(f"预测时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试数据量: {len(X_test)}条\n")
            f.write(f"特征数: {len(feature_names)}个\n")
            f.write(f"预测时间范围: {clean_test_data['时间'].min()} 至 {clean_test_data['时间'].max()}\n")
            f.write("\n预测统计信息:\n")
            f.write(f"  平均出力: {np.mean(predictions):.2f} MW\n")
            f.write(f"  最大出力: {np.max(predictions):.2f} MW\n")
            f.write(f"  最小出力: {np.min(predictions):.2f} MW\n")
            f.write(f"  标准差: {np.std(predictions):.2f} MW\n")
            f.write(f"  容量因子: {np.mean(predictions)/config['site_info']['capacity_mw']*100:.1f}%\n")
            
            # 时间段统计
            result_with_time = result_df.copy()
            result_with_time['时间'] = pd.to_datetime(result_with_time['时间'], format='%Y/%m/%d %H:%M')
            result_with_time['hour'] = result_with_time['时间'].dt.hour
            
            hourly_avg = result_with_time.groupby('hour')['出力(MW)'].mean()
            f.write("\n小时平均出力:\n")
            for hour, avg_power in hourly_avg.items():
                f.write(f"  {hour:02d}时: {avg_power:.2f} MW\n")
        
        logger.info(f"预测报告保存: {report_path}")
        
        # 8. 显示预测结果样例
        logger.info("预测结果样例:")
        logger.info(result_df.head(10).to_string(index=False))
        
        # 9. 预测完成
        elapsed_time = time.time() - start_time
        logger.info("="*50)
        logger.info(f"F5站点预测流程完成！耗时: {elapsed_time:.2f}秒")
        logger.info(f"预测结果保存路径: {output_path}")
        logger.info("="*50)
        
        return True
        
    except Exception as e:
        logger.error(f"F5站点预测失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("F5站点预测成功完成！")
        exit(0)
    else:
        print("F5站点预测失败！")
        exit(1)
