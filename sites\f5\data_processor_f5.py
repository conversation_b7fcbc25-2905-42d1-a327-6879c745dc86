"""
F5站点数据处理器
专门处理F5站点的数据加载、清洗和验证
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.base_classes import BaseDataProcessor
from sites.f5.config_f5 import get_f5_config


class DataProcessorF5(BaseDataProcessor):
    """F5站点数据处理器"""
    
    def __init__(self):
        super().__init__('f5')
        self.config = get_f5_config()
        self.site_info = self.config['site_info']
        self.data_config = self.config['data_processing']
        
    def load_data(self, file_path: str, is_train: bool = True) -> pd.DataFrame:
        """
        加载数据
        
        Args:
            file_path: 数据文件路径
            is_train: 是否为训练数据
            
        Returns:
            加载的数据DataFrame
        """
        try:
            self.logger.info(f"开始加载数据: {file_path}")

            # 读取CSV文件，处理中文编码
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试GBK编码
                try:
                    df = pd.read_csv(file_path, encoding='gbk')
                    self.logger.info("使用GBK编码读取数据")
                except UnicodeDecodeError:
                    # 如果GBK也失败，尝试自动检测编码
                    import chardet
                    with open(file_path, 'rb') as f:
                        raw_data = f.read()
                        encoding = chardet.detect(raw_data)['encoding']
                    df = pd.read_csv(file_path, encoding=encoding)
                    self.logger.info(f"使用自动检测编码: {encoding}")
            
            # 筛选F5站点数据
            df_f5 = df[df['站点编号'] == 'f5'].copy()
            
            self.logger.info(f"F5站点数据加载完成，共{len(df_f5)}行")
            
            # 处理时间列
            df_f5 = self._process_datetime(df_f5)
            
            # 筛选训练数据中的有效记录（有目标值）
            if is_train:
                df_f5 = df_f5.dropna(subset=['出力(MW)'])
                self.logger.info(f"筛选有效训练数据后，共{len(df_f5)}行")
            
            return df_f5
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {str(e)}")
            raise
    
    def _process_datetime(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理时间列"""
        try:
            # 转换时间格式
            df['时间'] = pd.to_datetime(df['时间'], format='%Y/%m/%d %H:%M')
            
            # 按时间排序
            df = df.sort_values('时间').reset_index(drop=True)
            
            self.logger.info("时间列处理完成")
            return df
            
        except Exception as e:
            self.logger.error(f"时间列处理失败: {str(e)}")
            raise
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            df: 原始数据
            
        Returns:
            清洗后的数据
        """
        try:
            self.logger.info("开始数据清洗")
            df_clean = df.copy()
            
            # 1. 处理缺失值
            df_clean = self._handle_missing_values(df_clean)
            
            # 2. 处理异常值
            df_clean = self._handle_outliers(df_clean)
            
            # 3. 数据平滑（小型站点特定）
            df_clean = self._smooth_data(df_clean)
            
            self.logger.info(f"数据清洗完成，最终数据量: {len(df_clean)}行")
            return df_clean
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {str(e)}")
            raise
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        missing_before = df.isnull().sum().sum()
        
        # 对气象变量进行线性插值
        weather_cols = ['10米风速（10m/s）', '温度（K）', '气压(Pa）', '相对湿度（%）', '10米风向（°)', '100m风速（100m/s）']
        for col in weather_cols:
            if col in df.columns:
                df[col] = df[col].interpolate(method='linear')
        
        # 前向填充剩余缺失值
        df = df.fillna(method='ffill')
        
        # 后向填充开头的缺失值
        df = df.fillna(method='bfill')
        
        missing_after = df.isnull().sum().sum()
        self.logger.info(f"缺失值处理: {missing_before} -> {missing_after}")
        
        return df
    
    def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理异常值（小型站点更严格）"""
        weather_cols = ['10米风速（10m/s）', '温度（K）', '气压(Pa）', '相对湿度（%）', '100m风速（100m/s）']
        
        for col in weather_cols:
            if col in df.columns:
                # 使用更严格的分位数
                q_low, q_high = self.data_config['outlier_quantiles']
                lower_bound = df[col].quantile(q_low)
                upper_bound = df[col].quantile(q_high)
                
                # 截断异常值
                outliers_before = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                df[col] = df[col].clip(lower_bound, upper_bound)
                
                if outliers_before > 0:
                    self.logger.info(f"{col}异常值处理: {outliers_before}个")
        
        return df
    
    def _smooth_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据平滑（小型站点特定）"""
        smooth_window = self.data_config['smoothing_window']
        weather_cols = ['10米风速（10m/s）', '温度（K）', '气压(Pa）', '相对湿度（%）', '100m风速（100m/s）']
        
        for col in weather_cols:
            if col in df.columns:
                # 移动平均平滑
                df[f'{col}_smooth'] = df[col].rolling(window=smooth_window, center=True).mean()
                df[f'{col}_smooth'] = df[f'{col}_smooth'].fillna(df[col])
        
        self.logger.info(f"数据平滑完成，窗口大小: {smooth_window}")
        return df
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """
        验证数据质量
        
        Args:
            df: 待验证的数据
            
        Returns:
            验证是否通过
        """
        try:
            self.logger.info("开始数据质量验证")
            
            # 1. 检查数据量
            if len(df) < 100:
                self.logger.error(f"数据量过少: {len(df)}行")
                return False
            
            # 2. 检查必要列
            required_cols = ['站点编号', '时间', '10米风速（10m/s）', '温度（K）']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                self.logger.error(f"缺少必要列: {missing_cols}")
                return False
            
            # 3. 检查站点编号
            if not all(df['站点编号'] == 'f5'):
                self.logger.error("存在非F5站点数据")
                return False
            
            # 4. 检查时间连续性
            time_diff = df['时间'].diff().dt.total_seconds() / 3600  # 小时
            expected_interval = 1.0  # 1小时间隔
            irregular_intervals = (abs(time_diff - expected_interval) > 0.1).sum()
            if irregular_intervals > len(df) * 0.05:  # 超过5%的不规律间隔
                self.logger.warning(f"时间间隔不规律的记录: {irregular_intervals}个")
            
            # 5. 检查数值范围
            if '出力(MW)' in df.columns:
                power_out_of_range = ((df['出力(MW)'] < 0) | (df['出力(MW)'] > 48)).sum()
                if power_out_of_range > 0:
                    self.logger.warning(f"出力超出范围的记录: {power_out_of_range}个")
            
            # 6. 检查风速合理性
            unrealistic_wind = ((df['10米风速（10m/s）'] < 0) | (df['10米风速（10m/s）'] > 50)).sum()
            if unrealistic_wind > 0:
                self.logger.warning(f"风速不合理的记录: {unrealistic_wind}个")
            
            self.logger.info("数据质量验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_train_test_split(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        时间序列数据分割
        
        Args:
            df: 完整数据
            
        Returns:
            训练集和验证集
        """
        split_ratio = self.config['validation']['train_test_split']
        split_idx = int(len(df) * (1 - split_ratio))
        
        train_df = df.iloc[:split_idx].copy()
        val_df = df.iloc[split_idx:].copy()
        
        self.logger.info(f"数据分割完成 - 训练集: {len(train_df)}行, 验证集: {len(val_df)}行")
        
        return train_df, val_df


if __name__ == "__main__":
    # 测试数据处理器
    processor = DataProcessorF5()
    
    # 测试加载训练数据
    config = get_f5_config()
    train_data = processor.load_data(str(config['data_paths']['train_data']), is_train=True)
    print(f"训练数据加载成功: {train_data.shape}")
    
    # 测试数据清洗
    clean_data = processor.clean_data(train_data)
    print(f"数据清洗完成: {clean_data.shape}")
    
    # 测试数据验证
    is_valid = processor.validate_data(clean_data)
    print(f"数据验证结果: {is_valid}")
    
    # 测试数据分割
    train_df, val_df = processor.get_train_test_split(clean_data)
    print(f"数据分割完成 - 训练: {train_df.shape}, 验证: {val_df.shape}")
