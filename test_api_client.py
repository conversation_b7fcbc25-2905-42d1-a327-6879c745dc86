"""
测试修改后的API服务客户端
"""

import requests
import pandas as pd
from pathlib import Path

def test_api_with_file():
    """测试使用文件上传的API"""
    
    # API服务地址
    api_url = "http://localhost:8888/predict"
    
    # 测试文件路径
    test_file_path = Path("data/test/测试集A_风电预测_气象变量.csv")
    
    if not test_file_path.exists():
        print(f"测试文件不存在: {test_file_path}")
        return
    
    # 读取少量测试数据
    df = pd.read_csv(test_file_path)
    # 只取前100行进行测试
    test_df = df.head(100)
    
    # 保存测试数据到临时文件
    temp_file = "temp_test_data.csv"
    test_df.to_csv(temp_file, index=False)
    
    try:
        # 发送文件到API
        with open(temp_file, 'rb') as f:
            files = {'file': ('test_data.csv', f, 'text/csv')}
            
            print("正在发送请求到API...")
            response = requests.post(api_url, files=files)
            
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n=== API响应解析 ===")
            print(f"状态: {result.get('status')}")
            print(f"结果数量: {len(result.get('results', []))}")
            
            for i, site_result in enumerate(result.get('results', [])):
                site = site_result.get('site')
                power = site_result.get('power', [])
                print(f"站点 {site}: {len(power)} 个预测值")
                if power:
                    print(f"  前5个值: {power[:5]}")
        else:
            print("API请求失败")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
    
    finally:
        # 清理临时文件
        import os
        if os.path.exists(temp_file):
            os.remove(temp_file)

def test_health_check():
    """测试健康检查接口"""
    try:
        response = requests.get("http://localhost:8888/health")
        print(f"健康检查响应: {response.status_code}")
        if response.status_code == 200:
            print(response.json())
    except Exception as e:
        print(f"健康检查失败: {str(e)}")

def test_service_info():
    """测试服务信息接口"""
    try:
        response = requests.get("http://localhost:8888/info")
        print(f"服务信息响应: {response.status_code}")
        if response.status_code == 200:
            import json
            print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"服务信息获取失败: {str(e)}")

if __name__ == "__main__":
    print("=== 测试API服务 ===")
    
    print("\n1. 测试健康检查...")
    test_health_check()
    
    print("\n2. 测试服务信息...")
    test_service_info()
    
    print("\n3. 测试文件上传预测...")
    test_api_with_file()
