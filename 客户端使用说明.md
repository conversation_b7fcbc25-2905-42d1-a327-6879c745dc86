# 风电预测API客户端使用说明

## 概述
`api请求.py` 是风电预测API的客户端工具，用于向API服务发送CSV文件并获取预测结果。

## 主要变更
客户端已更新以适配新的API接口：
- **输入方式**: 从JSON数据改为直接上传CSV文件
- **响应格式**: 适配新的API响应格式（status + results）
- **结果处理**: 将新格式的结果转换为传统的DataFrame格式保存

## 使用方法

### 基本用法
```bash
python api请求.py --input <输入CSV文件> --output <输出CSV文件> [--api-url <API地址>]
```

### 参数说明
- `--input`: 必需，输入的CSV文件路径（气象数据）
- `--output`: 必需，输出的CSV文件路径（预测结果）
- `--api-url`: 可选，API服务地址，默认为 `http://localhost:8888`
- `--check-only`: 可选，仅检查API服务状态，不执行预测

### 使用示例

#### 1. 执行预测
```bash
python api请求.py --input data/test/测试集A_风电预测_气象变量.csv --output predictions.csv
```

#### 2. 指定API地址
```bash
python api请求.py --input test_data.csv --output results.csv --api-url http://*************:8888
```

#### 3. 健康检查
```bash
python api请求.py --input dummy.csv --output dummy.csv --check-only
```

## 输入文件格式
输入的CSV文件必须包含以下列（与测试集格式相同）：
- 站点编号
- 时间
- 气压(Pa）
- 相对湿度（%）
- 云量
- 10米风速（10m/s）
- 10米风向（°)
- 温度（K）
- 辐照强度（J/m2）
- 降水（m）
- 100m风速（100m/s）
- 100m风向（°)

## 输出文件格式
输出的CSV文件包含以下列：
- `站点编号`: 风电场编号（f1-f5）
- `时间点`: 预测时间点索引
- `出力(MW)`: 预测的风电出力值（MW）

## 工作流程
1. **文件验证**: 检查输入文件是否存在
2. **服务检查**: 验证API服务是否可用
3. **文件上传**: 将CSV文件上传到API服务
4. **结果处理**: 接收API响应并转换格式
5. **结果保存**: 将预测结果保存为CSV文件

## 错误处理
客户端会处理以下错误情况：
- 输入文件不存在
- API服务不可用
- 网络连接错误
- 请求超时（5分钟）
- API返回错误

## 输出示例

### 成功执行
```
============================================================
风电预测API客户端
============================================================
正在检查API服务状态...
✅ API服务正常运行
   服务状态: healthy
   已加载站点: f1, f2, f3, f4, f5

输入文件: test_data.csv
  数据量: 100条记录
  列数: 11列
  站点: ['f1', 'f2']

正在发送预测请求...
✅ 预测请求成功完成，耗时: 2.35秒

正在保存预测结果到: predictions.csv
✅ 预测结果保存成功
  输出文件: predictions.csv
  记录数: 200

预测统计信息:
  预测站点数: 2
  F1站点:
    总预测点数: 100
    有效预测点数: 98
    平均出力: 15.23 MW
    最大出力: 45.67 MW
    最小出力: 0.12 MW
  F2站点:
    总预测点数: 100
    有效预测点数: 100
    平均出力: 125.45 MW
    最大出力: 275.89 MW
    最小出力: 5.23 MW

🎉 预测完成！结果已保存到: predictions.csv
```

### 健康检查
```
python api请求.py --input dummy.csv --output dummy.csv --check-only

============================================================
风电预测API客户端
============================================================
正在检查API服务状态...
✅ API服务正常运行
   服务状态: healthy
   已加载站点: f1, f2, f3, f4, f5

API服务信息:
  服务名称: wind_power_prediction_api
  版本: 2.0.0
  描述: 风电出力预测API服务
  支持的站点:
    f1: f1风电场 (48MW)
    f2: f2风电场 (280MW)
    f3: f3风电场 (48MW)
    f4: f4风电场 (88MW)
    f5: f5风电场 (48MW)
```

## 注意事项
1. 确保API服务正在运行（`python api服务.py`）
2. 输入文件必须是有效的CSV格式
3. 网络连接必须稳定
4. 大文件可能需要较长处理时间
5. 输出目录必须有写入权限

## 测试
使用提供的测试脚本验证客户端功能：
```bash
python test_client.py
```
