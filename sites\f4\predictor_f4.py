"""
F4站点预测器
专门为F4站点（48MW小型风电场）设计的预测器
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import joblib
from typing import Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.base_classes import BasePredictor
from sites.f4.config_f4 import get_f4_config


class PredictorF4(BasePredictor):
    """F4站点预测器"""
    
    def __init__(self):
        super().__init__('f4')
        self.config = get_f4_config()
        self.site_info = self.config['site_info']
        self.prediction_config = self.config['prediction_config']
        self.feature_names = None
        
    def load_model(self, model_path: str = None) -> None:
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
        """
        try:
            if model_path is None:
                model_dir = self.config['data_paths']['model_dir']
                model_path = model_dir / 'f4_lgb_model.pkl'
            
            self.logger.info(f"加载F4站点模型: {model_path}")
            
            # 加载模型
            self.model = joblib.load(model_path)
            
            # 加载参数（如果存在）
            params_path = Path(model_path).parent / 'f4_model_params.pkl'
            if params_path.exists():
                self.model_params = joblib.load(params_path)
                self.logger.info("模型参数加载成功")
            
            self.logger.info("F4站点模型加载成功")
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        执行预测
        
        Args:
            X: 特征数据
            
        Returns:
            预测结果
        """
        try:
            if self.model is None:
                raise ValueError("模型未加载")
            
            self.logger.info(f"开始F4站点预测，数据量: {len(X)}")
            
            # 执行预测
            predictions = self.model.predict(X)
            
            # 应用容量约束
            if self.prediction_config['capacity_constraint']:
                predictions = np.clip(
                    predictions,
                    self.prediction_config['min_output'],
                    self.prediction_config['max_output']
                )
            
            # 后处理
            if self.prediction_config['post_processing']['smooth_predictions']:
                predictions = self._smooth_predictions(predictions)
            
            self.logger.info("F4站点预测完成")
            return predictions
            
        except Exception as e:
            self.logger.error(f"预测失败: {str(e)}")
            raise
    
    def _smooth_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """平滑预测结果"""
        window = self.prediction_config['post_processing']['smooth_window']
        
        # 使用移动平均进行平滑
        smoothed = np.convolve(predictions, np.ones(window)/window, mode='same')
        
        # 处理边界
        smoothed[:window//2] = predictions[:window//2]
        smoothed[-window//2:] = predictions[-window//2:]
        
        return smoothed
    
    def format_predictions(self, predictions: np.ndarray, timestamps: pd.Series) -> pd.DataFrame:
        """
        格式化预测结果
        
        Args:
            predictions: 预测值
            timestamps: 时间戳
            
        Returns:
            格式化的预测结果
        """
        try:
            self.logger.info("格式化F4站点预测结果")
            
            # 创建结果DataFrame
            result_df = pd.DataFrame({
                '站点编号': 'f4',
                '时间': timestamps,
                '出力(MW)': predictions
            })
            
            # 格式化时间
            result_df['时间'] = result_df['时间'].dt.strftime(
                self.prediction_config['time_format']
            )
            
            # 确保列顺序
            result_df = result_df[self.prediction_config['output_columns']]
            
            self.logger.info(f"预测结果格式化完成，共{len(result_df)}条记录")
            return result_df
            
        except Exception as e:
            self.logger.error(f"预测结果格式化失败: {str(e)}")
            raise
    
    def save_predictions(self, predictions_df: pd.DataFrame, output_path: str = None) -> None:
        """
        保存预测结果
        
        Args:
            predictions_df: 预测结果DataFrame
            output_path: 输出文件路径
        """
        try:
            if output_path is None:
                output_dir = self.config['data_paths']['output_dir']
                output_path = output_dir / 'f4_predictions.csv'
            
            # 确保目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存预测结果
            predictions_df.to_csv(
                output_path,
                index=False,
                encoding='utf-8'
            )
            
            self.logger.info(f"F4站点预测结果保存成功: {output_path}")
            
        except Exception as e:
            self.logger.error(f"预测结果保存失败: {str(e)}")
            raise
    
    def predict_from_test_data(self, test_data_path: str = None) -> pd.DataFrame:
        """
        从测试数据进行完整预测流程
        
        Args:
            test_data_path: 测试数据路径
            
        Returns:
            预测结果DataFrame
        """
        try:
            self.logger.info("开始F4站点完整预测流程")
            
            # 导入必要的模块
            from sites.f4.data_processor_f4 import DataProcessorF4
            from sites.f4.feature_engineer_f4 import FeatureEngineerF4
            
            # 加载测试数据
            if test_data_path is None:
                test_data_path = self.config['data_paths']['test_data']
            
            processor = DataProcessorF4()
            test_data = processor.load_data(str(test_data_path), is_train=False)
            clean_test_data = processor.clean_data(test_data)
            
            # 特征工程
            engineer = FeatureEngineerF4()
            features_data = engineer.extract_features(clean_test_data)
            
            # 注意：测试数据没有目标变量，所以不进行特征选择
            # 使用训练时保存的特征名称
            if self.feature_names is None:
                # 如果没有保存的特征名称，使用所有特征
                feature_cols = engineer.get_feature_names()
            else:
                feature_cols = self.feature_names
            
            # 确保特征列存在
            available_features = [col for col in feature_cols if col in features_data.columns]
            if len(available_features) != len(feature_cols):
                missing_features = set(feature_cols) - set(available_features)
                self.logger.warning(f"缺少特征: {missing_features}")
            
            X_test = features_data[available_features]
            
            # 执行预测
            predictions = self.predict(X_test)
            
            # 格式化结果
            result_df = self.format_predictions(predictions, clean_test_data['时间'])
            
            self.logger.info("F4站点完整预测流程完成")
            return result_df
            
        except Exception as e:
            self.logger.error(f"完整预测流程失败: {str(e)}")
            raise
    
    def set_feature_names(self, feature_names: list) -> None:
        """
        设置特征名称列表
        
        Args:
            feature_names: 特征名称列表
        """
        self.feature_names = feature_names
        self.logger.info(f"设置特征名称，共{len(feature_names)}个特征")
    
    def validate_predictions(self, predictions: np.ndarray) -> bool:
        """
        验证预测结果
        
        Args:
            predictions: 预测值
            
        Returns:
            验证是否通过
        """
        try:
            # 检查预测值范围
            min_pred = np.min(predictions)
            max_pred = np.max(predictions)
            
            if min_pred < 0:
                self.logger.warning(f"存在负预测值: {min_pred}")
            
            if max_pred > self.site_info['capacity_mw']:
                self.logger.warning(f"预测值超过装机容量: {max_pred} > {self.site_info['capacity_mw']}")
            
            # 检查异常值
            q99 = np.percentile(predictions, 99)
            if q99 > self.site_info['capacity_mw'] * 0.9:
                self.logger.info(f"99%分位数: {q99:.2f}MW")
            
            # 检查NaN值
            nan_count = np.isnan(predictions).sum()
            if nan_count > 0:
                self.logger.error(f"存在NaN预测值: {nan_count}个")
                return False
            
            self.logger.info("预测结果验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"预测结果验证失败: {str(e)}")
            return False


if __name__ == "__main__":
    # 测试预测器
    try:
        print("开始测试F4站点预测器...")
        
        # 创建预测器
        predictor = PredictorF4()
        
        # 加载模型（如果存在）
        config = get_f4_config()
        model_path = config['data_paths']['model_dir'] / 'f4_lgb_model.pkl'
        
        if model_path.exists():
            predictor.load_model(str(model_path))
            print("模型加载成功")
            
            # 进行预测
            result_df = predictor.predict_from_test_data()
            print(f"预测完成: {result_df.shape}")
            print(result_df.head())
            
            # 保存预测结果
            predictor.save_predictions(result_df)
            print("预测结果保存完成")
            
        else:
            print(f"模型文件不存在: {model_path}")
            print("请先运行训练脚本")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
