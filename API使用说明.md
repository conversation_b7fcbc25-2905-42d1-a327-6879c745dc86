# 风电预测API使用说明

## 概述
风电预测API服务提供基于气象数据的风电出力预测功能。API已更新为文件上传方式，支持CSV格式的气象数据输入。

## API接口

### 1. 预测接口
- **URL**: `POST /predict`
- **Content-Type**: `multipart/form-data`

#### 请求参数
| 参数代码 | 参数名称 | 数据格式 | 非空 | 参数说明 |
|---------|---------|---------|------|---------|
| file | 测试集气象数据 | file | 是 | 包含天气变量的csv文件 |

#### CSV文件格式要求
CSV文件应包含以下列（与`data/test/测试集A_风电预测_气象变量.csv`格式相同）：
- 站点编号
- 时间
- 气压(Pa）
- 相对湿度（%）
- 云量
- 10米风速（10m/s）
- 10米风向（°)
- 温度（K）
- 辐照强度（J/m2）
- 降水（m）
- 100m风速（100m/s）
- 100m风向（°)

#### 返回参数
| 参数代码 | 参数名称 | 数据格式 | 非空 | 参数说明 |
|---------|---------|---------|------|---------|
| status | 结果状态码 | int | 是 | 结果状态码，0失败，1成功 |
| results | 返回结果 | list | 是 | 返回结果，包含site、power字段 |
| +site | 站点 | string | 是 | 风电场名称 |
| +power | 预测功率 | array | 是 | 预测功率，缺失值用null占位 |

#### 返回示例

**成功响应**:
```json
{
    "status": 1,
    "results": [
        {
            "site": "f1",
            "power": [17.26, 16.78, 12.43, null, 15.2]
        },
        {
            "site": "f2",
            "power": [15.2, 14.8, 14.3, 16.1, null]
        }
    ]
}
```

**失败响应**:
```json
{
    "status": 0,
    "results": []
}
```

### 2. 健康检查接口
- **URL**: `GET /health`
- **说明**: 检查服务运行状态

### 3. 服务信息接口
- **URL**: `GET /info`
- **说明**: 获取服务详细信息和API文档

## 使用示例

### Python客户端示例
```python
import requests

# 上传CSV文件进行预测
url = "http://localhost:8888/predict"
with open("test_data.csv", "rb") as f:
    files = {"file": ("test_data.csv", f, "text/csv")}
    response = requests.post(url, files=files)

if response.status_code == 200:
    result = response.json()
    if result["status"] == 1:
        print("预测成功")
        for site_result in result["results"]:
            site = site_result["site"]
            power = site_result["power"]
            print(f"站点 {site}: {len(power)} 个预测值")
    else:
        print("预测失败")
else:
    print(f"请求失败: {response.status_code}")
```

### curl命令示例
```bash
curl -X POST \
  http://localhost:8888/predict \
  -F "file=@test_data.csv"
```

## 启动服务
```bash
python api服务.py
```

服务将在 `http://localhost:8888` 启动。

## 支持的站点
- f1: f1风电场 (48MW)
- f2: f2风电场 (280MW)
- f3: f3风电场 (48MW)
- f4: f4风电场 (88MW)
- f5: f5风电场 (48MW)

## 注意事项
1. CSV文件必须包含所有必需的气象变量列
2. 站点编号必须是支持的站点之一（f1-f5）
3. 时间格式应为 "YYYY/M/D H:MM" 格式
4. 预测结果中的null值表示该时间点的预测值缺失
5. 文件大小建议不超过10MB以确保处理性能
