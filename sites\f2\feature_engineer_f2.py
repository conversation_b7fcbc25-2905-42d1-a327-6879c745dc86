"""
F2站点特征工程器
专门为F2站点（48MW小型风电场）设计的特征工程
注重灵敏度和快速响应特征
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from typing import List, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.base_classes import BaseFeatureEngineer
from sites.f2.config_f2 import get_f2_config


class FeatureEngineerF2(BaseFeatureEngineer):
    """F2站点特征工程器"""
    
    def __init__(self):
        super().__init__('f2')
        self.config = get_f2_config()
        self.feature_config = self.config['feature_config']
        self.feature_names = []
        
    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取F2站点特征
        
        Args:
            df: 输入数据
            
        Returns:
            包含特征的数据
        """
        try:
            self.logger.info("开始F2站点特征提取")
            df_features = df.copy()
            
            # 1. 基础时间特征
            df_features = self._extract_time_features(df_features)
            
            # 2. 小型站点特定特征（灵敏度特征）
            df_features = self._extract_wind_sensitivity_features(df_features)
            
            # 3. 短期滞后特征
            df_features = self._extract_lag_features(df_features)
            
            # 4. 高频统计特征
            df_features = self._extract_rolling_features(df_features)
            
            # 5. 气象特征交互
            df_features = self._extract_interaction_features(df_features)
            
            # 6. 技术指标特征
            df_features = self._extract_technical_features(df_features)

            # 7. 异常检测特征
            df_features = self._extract_anomaly_features(df_features)

            # 8. 功率曲线建模特征
            df_features = self._extract_power_curve_features(df_features)

            # 移除无限值和NaN
            df_features = self._clean_features(df_features)
            
            self.logger.info(f"F2站点特征提取完成，特征数: {len(self.feature_names)}")
            return df_features
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {str(e)}")
            raise
    
    def _extract_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取时间特征"""
        # 基础时间特征
        df['hour'] = df['时间'].dt.hour
        df['day_of_week'] = df['时间'].dt.dayofweek
        df['month'] = df['时间'].dt.month
        df['day_of_year'] = df['时间'].dt.dayofyear
        df['week_of_year'] = df['时间'].dt.isocalendar().week

        # 季节特征（基于F2数据分析的季节性模式）
        df['season'] = df['month'].map({
            12: 0, 1: 0, 2: 0,  # 冬季（高出力期）
            3: 1, 4: 1, 5: 1,   # 春季（中等出力期）
            6: 2, 7: 2, 8: 2,   # 夏季（低出力期）
            9: 3, 10: 3, 11: 3  # 秋季（恢复期）
        })

        # 高出力季节标识（基于数据分析：1-2月、10-12月）
        df['high_power_season'] = df['month'].isin([1, 2, 10, 11, 12]).astype(int)

        # 低出力季节标识（基于数据分析：6-8月）
        df['low_power_season'] = df['month'].isin([6, 7, 8]).astype(int)

        # 周末标识
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)

        # 时间段特征（基于F2数据分析的日内模式）
        df['morning_peak'] = ((df['hour'] >= 14) & (df['hour'] <= 18)).astype(int)  # 下午高峰
        df['night_low'] = ((df['hour'] >= 5) & (df['hour'] <= 9)).astype(int)       # 早晨低谷

        # 时间周期特征（正弦余弦编码）
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['week_of_year_sin'] = np.sin(2 * np.pi * df['week_of_year'] / 52)
        df['week_of_year_cos'] = np.cos(2 * np.pi * df['week_of_year'] / 52)

        time_features = [
            'hour', 'day_of_week', 'month', 'day_of_year', 'week_of_year', 'season',
            'high_power_season', 'low_power_season', 'is_weekend', 'morning_peak', 'night_low',
            'hour_sin', 'hour_cos', 'month_sin', 'month_cos',
            'day_of_year_sin', 'day_of_year_cos', 'week_of_year_sin', 'week_of_year_cos'
        ]
        self.feature_names.extend(time_features)

        self.logger.info(f"时间特征提取完成: {len(time_features)}个")
        return df
    
    def _extract_wind_sensitivity_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取风速敏感性特征（大型站点特定）"""
        wind_col = '10米风速（10m/s）'
        wind_100_col = '100m风速（100m/s）'

        # 风速变化率特征
        for window in self.feature_config['wind_sensitivity_features']['wind_change_windows']:
            df[f'wind_speed_change_{window}h'] = df[wind_col].diff(window)
            df[f'wind_speed_pct_change_{window}h'] = df[wind_col].pct_change(window)
            self.feature_names.extend([f'wind_speed_change_{window}h', f'wind_speed_pct_change_{window}h'])

        # 风速波动性特征
        for window in self.feature_config['wind_sensitivity_features']['volatility_windows']:
            df[f'wind_speed_volatility_{window}h'] = df[wind_col].rolling(window).std()
            df[f'wind_speed_range_{window}h'] = (df[wind_col].rolling(window).max() -
                                                df[wind_col].rolling(window).min())
            self.feature_names.extend([f'wind_speed_volatility_{window}h', f'wind_speed_range_{window}h'])

        # 风速加速度特征
        change_windows = self.feature_config['wind_sensitivity_features']['wind_change_windows']
        for window in self.feature_config['wind_sensitivity_features']['acceleration_windows']:
            if window in change_windows:
                df[f'wind_speed_acceleration_{window}h'] = df[f'wind_speed_change_{window}h'].diff(1)
                self.feature_names.append(f'wind_speed_acceleration_{window}h')

        # 风速比值特征（如果有100米风速）
        if wind_100_col in df.columns:
            df['wind_speed_ratio_100_10'] = df[wind_100_col] / (df[wind_col] + 0.1)
            df['wind_shear'] = np.log(df[wind_100_col] / (df[wind_col] + 0.1)) / np.log(100/10)
            self.feature_names.extend(['wind_speed_ratio_100_10', 'wind_shear'])

        # 大型风电场功率曲线特征
        df['wind_speed_cut_in'] = (df[wind_col] >= 3.0).astype(int)  # 切入风速
        df['wind_speed_rated'] = (df[wind_col] >= 12.0).astype(int)  # 额定风速
        df['wind_speed_cut_out'] = (df[wind_col] >= 25.0).astype(int)  # 切出风速

        # 风速功率曲线分段特征（基于F2数据分析）
        df['wind_power_zone_1'] = ((df[wind_col] >= 0) & (df[wind_col] < 4)).astype(int)    # 低风速区
        df['wind_power_zone_2'] = ((df[wind_col] >= 4) & (df[wind_col] < 8)).astype(int)    # 爬坡区
        df['wind_power_zone_3'] = ((df[wind_col] >= 8) & (df[wind_col] < 12)).astype(int)   # 高效区
        df['wind_power_zone_4'] = ((df[wind_col] >= 12) & (df[wind_col] < 16)).astype(int)  # 额定区
        df['wind_power_zone_5'] = (df[wind_col] >= 16).astype(int)                          # 限功率区

        # 风速立方特征（风能密度）
        df['wind_speed_cubed'] = df[wind_col] ** 3
        df['wind_speed_squared'] = df[wind_col] ** 2

        # 风速趋势特征
        df['wind_speed_trend_3h'] = df[wind_col].rolling(3).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 3 else 0)
        df['wind_speed_trend_6h'] = df[wind_col].rolling(6).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 6 else 0)

        self.feature_names.extend([
            'wind_speed_cut_in', 'wind_speed_rated', 'wind_speed_cut_out',
            'wind_power_zone_1', 'wind_power_zone_2', 'wind_power_zone_3', 'wind_power_zone_4', 'wind_power_zone_5',
            'wind_speed_cubed', 'wind_speed_squared', 'wind_speed_trend_3h', 'wind_speed_trend_6h'
        ])

        self.logger.info("风速敏感性特征提取完成")
        return df
    
    def _extract_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取滞后特征（小型站点短期滞后）"""
        weather_cols = ['10米风速（10m/s）', '温度（K）', '气压(Pa）', '相对湿度（%）']
        
        for col in weather_cols:
            if col in df.columns:
                for lag in self.feature_config['lag_features']:
                    df[f'{col}_lag_{lag}h'] = df[col].shift(lag)
                    self.feature_names.append(f'{col}_lag_{lag}h')
        
        self.logger.info(f"滞后特征提取完成: {len(self.feature_config['lag_features'])}个滞后期")
        return df
    
    def _extract_rolling_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取滚动统计特征（高频统计）"""
        weather_cols = ['10米风速（10m/s）', '温度（K）', '气压(Pa）', '相对湿度（%）']
        
        for col in weather_cols:
            if col in df.columns:
                for window in self.feature_config['rolling_windows']:
                    # 基础统计
                    df[f'{col}_mean_{window}h'] = df[col].rolling(window).mean()
                    df[f'{col}_std_{window}h'] = df[col].rolling(window).std()
                    df[f'{col}_min_{window}h'] = df[col].rolling(window).min()
                    df[f'{col}_max_{window}h'] = df[col].rolling(window).max()
                    
                    # 高级统计
                    df[f'{col}_skew_{window}h'] = df[col].rolling(window).skew()
                    df[f'{col}_kurt_{window}h'] = df[col].rolling(window).kurt()
                    
                    # 分位数特征
                    df[f'{col}_q25_{window}h'] = df[col].rolling(window).quantile(0.25)
                    df[f'{col}_q75_{window}h'] = df[col].rolling(window).quantile(0.75)
                    
                    features = [f'{col}_mean_{window}h', f'{col}_std_{window}h', 
                               f'{col}_min_{window}h', f'{col}_max_{window}h',
                               f'{col}_skew_{window}h', f'{col}_kurt_{window}h',
                               f'{col}_q25_{window}h', f'{col}_q75_{window}h']
                    self.feature_names.extend(features)
        
        self.logger.info("滚动统计特征提取完成")
        return df
    
    def _extract_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取交互特征"""
        wind_col = '10米风速（10m/s）'
        temp_col = '温度（K）'
        pressure_col = '气压(Pa）'
        humidity_col = '相对湿度（%）'

        # 风速与温度交互（温度影响空气密度）
        if wind_col in df.columns and temp_col in df.columns:
            df['wind_temp_interaction'] = df[wind_col] * df[temp_col]
            df['wind_temp_ratio'] = df[wind_col] / (df[temp_col] + 0.1)
            # 空气密度近似（基于温度）
            df['air_density_approx'] = 101325 / (287.05 * df[temp_col])
            df['wind_power_density'] = 0.5 * df['air_density_approx'] * (df[wind_col] ** 3)
            self.feature_names.extend(['wind_temp_interaction', 'wind_temp_ratio', 'air_density_approx', 'wind_power_density'])

        # 风速与气压交互（气压影响空气密度）
        if wind_col in df.columns and pressure_col in df.columns:
            df['wind_pressure_interaction'] = df[wind_col] * df[pressure_col]
            df['wind_pressure_ratio'] = df[wind_col] / (df[pressure_col] / 100000 + 0.1)  # 标准化气压
            self.feature_names.extend(['wind_pressure_interaction', 'wind_pressure_ratio'])

        # 温度与湿度交互
        if temp_col in df.columns and humidity_col in df.columns:
            df['temp_humidity_interaction'] = df[temp_col] * df[humidity_col]
            # 露点温度近似
            df['dew_point_approx'] = df[temp_col] - ((100 - df[humidity_col]) / 5)
            self.feature_names.extend(['temp_humidity_interaction', 'dew_point_approx'])

        # 风速与时间交互（季节性风速模式）
        if wind_col in df.columns:
            df['wind_season_interaction'] = df[wind_col] * df['season']
            df['wind_hour_interaction'] = df[wind_col] * df['hour']
            df['wind_month_interaction'] = df[wind_col] * df['month']
            self.feature_names.extend(['wind_season_interaction', 'wind_hour_interaction', 'wind_month_interaction'])

        # 综合气象指数
        if all(col in df.columns for col in [wind_col, temp_col, pressure_col, humidity_col]):
            # 标准化各变量
            wind_norm = (df[wind_col] - df[wind_col].mean()) / (df[wind_col].std() + 1e-8)
            temp_norm = (df[temp_col] - df[temp_col].mean()) / (df[temp_col].std() + 1e-8)
            pressure_norm = (df[pressure_col] - df[pressure_col].mean()) / (df[pressure_col].std() + 1e-8)
            humidity_norm = (df[humidity_col] - df[humidity_col].mean()) / (df[humidity_col].std() + 1e-8)

            # 综合气象指数（基于主成分分析思想）
            df['weather_composite_index'] = (0.6 * wind_norm + 0.2 * pressure_norm - 0.1 * temp_norm + 0.1 * humidity_norm)
            self.feature_names.append('weather_composite_index')

        self.logger.info("交互特征提取完成")
        return df
    
    def _extract_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取技术指标特征"""
        wind_col = '10米风速（10m/s）'
        
        if wind_col in df.columns:
            # 移动平均线
            for window in [3, 6, 12]:
                df[f'wind_ma_{window}'] = df[wind_col].rolling(window).mean()
                # 价格相对于移动平均线的位置
                df[f'wind_ma_ratio_{window}'] = df[wind_col] / (df[f'wind_ma_{window}'] + 0.1)
                self.feature_names.extend([f'wind_ma_{window}', f'wind_ma_ratio_{window}'])
            
            # RSI指标（相对强弱指数）
            delta = df[wind_col].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / (loss + 1e-8)
            df['wind_rsi'] = 100 - (100 / (1 + rs))
            self.feature_names.append('wind_rsi')
        
        self.logger.info("技术指标特征提取完成")
        return df

    def _extract_anomaly_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取异常检测特征"""
        wind_col = '10米风速（10m/s）'

        if wind_col in df.columns:
            # 风速异常检测（基于统计方法）
            wind_mean = df[wind_col].rolling(24).mean()
            wind_std = df[wind_col].rolling(24).std()

            # Z-score异常检测
            df['wind_zscore'] = (df[wind_col] - wind_mean) / (wind_std + 1e-8)
            df['wind_is_outlier'] = (np.abs(df['wind_zscore']) > 3).astype(int)

            # 风速突变检测
            df['wind_sudden_change'] = (np.abs(df[wind_col].diff()) > 5).astype(int)

            # 风速平稳性检测
            df['wind_stability'] = df[wind_col].rolling(6).std() / (df[wind_col].rolling(6).mean() + 1e-8)

            # 基于历史数据的异常检测
            df['wind_percentile_rank'] = df[wind_col].rolling(168).rank(pct=True)  # 一周窗口
            df['wind_extreme_low'] = (df['wind_percentile_rank'] < 0.05).astype(int)
            df['wind_extreme_high'] = (df['wind_percentile_rank'] > 0.95).astype(int)

            self.feature_names.extend([
                'wind_zscore', 'wind_is_outlier', 'wind_sudden_change', 'wind_stability',
                'wind_percentile_rank', 'wind_extreme_low', 'wind_extreme_high'
            ])

        self.logger.info("异常检测特征提取完成")
        return df

    def _extract_power_curve_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取功率曲线建模特征"""
        wind_col = '10米风速（10m/s）'

        if wind_col in df.columns:
            # 理论功率曲线特征（基于典型风电机组特性）
            wind_speed = df[wind_col]

            # 分段线性功率曲线近似
            def theoretical_power_curve(ws):
                """理论功率曲线（280MW大型风电场）"""
                if ws < 3:
                    return 0
                elif ws < 12:
                    return 280 * ((ws - 3) / 9) ** 3  # 立方增长
                elif ws < 25:
                    return 280  # 额定功率
                else:
                    return 0  # 切出

            df['theoretical_power'] = wind_speed.apply(theoretical_power_curve)

            # 功率曲线效率特征
            df['power_curve_efficiency'] = np.where(
                df['theoretical_power'] > 0,
                np.minimum(wind_speed / 12, 1.0),  # 相对于额定风速的效率
                0
            )

            # 风速功率密度特征
            df['wind_power_density_theoretical'] = 0.5 * 1.225 * (wind_speed ** 3)  # 标准空气密度

            # 功率曲线区间特征（更细分）
            df['power_curve_zone'] = pd.cut(
                wind_speed,
                bins=[0, 3, 6, 9, 12, 15, 25, 50],
                labels=[0, 1, 2, 3, 4, 5, 6],
                include_lowest=True
            ).astype(float)

            # 风速相对位置特征
            df['wind_relative_to_rated'] = wind_speed / 12.0  # 相对于额定风速
            df['wind_relative_to_cutout'] = wind_speed / 25.0  # 相对于切出风速

            # 风速变化对功率的影响
            wind_change = df[wind_col].diff()
            df['wind_change_impact'] = np.where(
                (wind_speed >= 3) & (wind_speed <= 12),
                wind_change * 3,  # 在爬坡区变化影响更大
                wind_change
            )

            self.feature_names.extend([
                'theoretical_power', 'power_curve_efficiency', 'wind_power_density_theoretical',
                'power_curve_zone', 'wind_relative_to_rated', 'wind_relative_to_cutout', 'wind_change_impact'
            ])

        self.logger.info("功率曲线特征提取完成")
        return df

    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理特征"""
        # 替换无限值
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # 前向填充NaN值
        feature_cols = [col for col in df.columns if col in self.feature_names]
        df[feature_cols] = df[feature_cols].fillna(method='ffill')
        df[feature_cols] = df[feature_cols].fillna(method='bfill')
        df[feature_cols] = df[feature_cols].fillna(0)
        
        return df
    
    def select_features(self, df: pd.DataFrame, target: Optional[pd.Series] = None) -> pd.DataFrame:
        """
        特征选择
        
        Args:
            df: 包含特征的数据
            target: 目标变量（可选）
            
        Returns:
            选择后的特征数据
        """
        try:
            self.logger.info("开始特征选择")
            
            # 获取特征列
            feature_cols = [col for col in self.feature_names if col in df.columns]
            
            if target is not None:
                # 基于相关性的特征选择
                feature_cols = self._correlation_based_selection(df[feature_cols], target)
                
                # 基于重要性的特征选择（如果有目标变量）
                feature_cols = self._importance_based_selection(df[feature_cols], target)
            
            # 移除高相关性特征
            feature_cols = self._remove_high_correlation_features(df[feature_cols])
            
            # 更新特征名称列表
            self.feature_names = feature_cols
            
            self.logger.info(f"特征选择完成，最终特征数: {len(feature_cols)}")
            
            # 返回选择的特征
            return df[['站点编号', '时间'] + feature_cols]
            
        except Exception as e:
            self.logger.error(f"特征选择失败: {str(e)}")
            raise
    
    def _correlation_based_selection(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """基于相关性的特征选择"""
        correlations = X.corrwith(y).abs()
        threshold = self.feature_config['feature_selection']['importance_threshold']
        selected_features = correlations[correlations > threshold].index.tolist()
        
        self.logger.info(f"相关性筛选: {len(X.columns)} -> {len(selected_features)}")
        return selected_features
    
    def _importance_based_selection(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """基于重要性的特征选择"""
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.feature_selection import SelectFromModel
            
            # 使用随机森林进行特征选择
            rf = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
            selector = SelectFromModel(rf, threshold='median')
            
            # 处理NaN值
            X_clean = X.fillna(0)
            y_clean = y.fillna(y.mean())
            
            selector.fit(X_clean, y_clean)
            selected_features = X.columns[selector.get_support()].tolist()
            
            self.logger.info(f"重要性筛选: {len(X.columns)} -> {len(selected_features)}")
            return selected_features
            
        except Exception as e:
            self.logger.warning(f"重要性筛选失败，使用全部特征: {str(e)}")
            return X.columns.tolist()
    
    def _remove_high_correlation_features(self, X: pd.DataFrame) -> List[str]:
        """移除高相关性特征"""
        corr_matrix = X.corr().abs()
        threshold = self.feature_config['feature_selection']['correlation_threshold']
        
        # 找到高相关性特征对
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                if corr_matrix.iloc[i, j] > threshold:
                    high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j]))
        
        # 移除高相关性特征中的一个
        features_to_remove = set()
        for feat1, feat2 in high_corr_pairs:
            features_to_remove.add(feat2)  # 移除第二个特征
        
        selected_features = [col for col in X.columns if col not in features_to_remove]
        
        self.logger.info(f"高相关性筛选: {len(X.columns)} -> {len(selected_features)}")
        return selected_features
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_names.copy()


if __name__ == "__main__":
    # 测试特征工程器
    try:
        from sites.f2.data_processor_f2 import DataProcessorF2

        # 加载数据
        processor = DataProcessorF2()
        config = get_f2_config()
        train_data = processor.load_data(str(config['data_paths']['train_data']), is_train=True)
        clean_data = processor.clean_data(train_data)

        # 特征工程
        engineer = FeatureEngineerF2()
        features_data = engineer.extract_features(clean_data)
        print(f"特征提取完成: {features_data.shape}")

        # 特征选择
        target = clean_data['出力(MW)']
        selected_data = engineer.select_features(features_data, target)
        print(f"特征选择完成: {selected_data.shape}")

        # 显示特征名称
        feature_names = engineer.get_feature_names()
        print(f"最终特征数: {len(feature_names)}")
        print("前10个特征:", feature_names[:10])

    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
