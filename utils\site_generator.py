"""
站点代码生成器
基于F1站点模板快速生成其他站点的代码
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Any

# 站点配置映射
SITE_CONFIGS = {
    'f2': {
        'name': 'f2风电场',
        'capacity_mw': 280,
        'type': 'large',
        'optuna_trials': 100,
        'max_features': 120,
        'lag_features': [1, 3, 6, 12, 24, 48],
        'rolling_windows': [6, 12, 24, 48],
        'hyperparameter_space': 'large_site'
    },
    'f3': {
        'name': 'f3风电场',
        'capacity_mw': 48,
        'type': 'small',
        'optuna_trials': 50,
        'max_features': 80,
        'lag_features': [1, 2, 3, 6, 12],
        'rolling_windows': [3, 6, 12],
        'hyperparameter_space': 'small_site'
    },
    'f4': {
        'name': 'f4风电场',
        'capacity_mw': 88,
        'type': 'medium',
        'optuna_trials': 75,
        'max_features': 100,
        'lag_features': [1, 2, 3, 6, 12, 24],
        'rolling_windows': [3, 6, 12, 24],
        'hyperparameter_space': 'medium_site'
    },
    'f5': {
        'name': 'f5风电场',
        'capacity_mw': 48,
        'type': 'small',
        'optuna_trials': 50,
        'max_features': 80,
        'lag_features': [1, 2, 3, 6, 12],
        'rolling_windows': [3, 6, 12],
        'hyperparameter_space': 'small_site'
    }
}

def generate_site_config(site_id: str) -> str:
    """生成站点配置文件"""
    config = SITE_CONFIGS[site_id]
    
    template = f'''"""
{config['name']}配置文件
{config['capacity_mw']}MW{config['type']}风电场配置
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.common_constants import *

# {site_id.upper()}站点基本信息
{site_id.upper()}_CONFIG = {{
    'site_info': {{
        'site_id': '{site_id}',
        'name': '{config['name']}',
        'capacity_mw': {config['capacity_mw']},
        'type': '{config['type']}'
    }},
    
    # 数据路径配置
    'data_paths': {{
        'train_data': DATA_PATHS['train'],
        'test_data': DATA_PATHS['test'],
        'model_dir': PATHS['models'] / 'models_{site_id}',
        'output_dir': PATHS['submissions'],
        'log_dir': PATHS['logs']
    }},
    
    # 特征工程配置（{config['type']}站点特定）
    'feature_config': {{
        'time_features': TIME_FEATURES['extended'],
        'lag_features': {config['lag_features']},
        'rolling_windows': {config['rolling_windows']},
        'weather_features': WEATHER_FEATURES,
        
        # {config['type']}站点特定特征
        'wind_sensitivity_features': {{
            'wind_change_windows': [1, 3, 6] if '{config['type']}' == 'small' else [3, 6, 12],
            'volatility_windows': [3, 6, 12] if '{config['type']}' == 'small' else [6, 12, 24],
            'acceleration_windows': [1, 2, 3] if '{config['type']}' == 'small' else [2, 3, 6]
        }},
        
        # 特征选择配置
        'feature_selection': {{
            'correlation_threshold': 0.95,
            'importance_threshold': 0.001,
            'max_features': {config['max_features']}
        }}
    }},
    
    # 模型配置（{config['type']}站点优化）
    'model_config': {{
        'lgb_base_params': {{
            **MODEL_CONFIG['lgb_base_params'],
            'max_depth': 6 if '{config['type']}' == 'small' else 8,
            'min_data_in_leaf': 20 if '{config['type']}' == 'small' else 50,
            'lambda_l1': 0.1,
            'lambda_l2': 0.1
        }},
        
        'optuna_config': {{
            'n_trials': {config['optuna_trials']},
            'timeout': 1800 if '{config['type']}' == 'small' else 3600,
            'cv_folds': 5,
            'scoring': 'neg_root_mean_squared_error'
        }},
        
        'hyperparameter_space': HYPERPARAMETER_SPACE['{config['hyperparameter_space']}']
    }},
    
    # 数据处理配置
    'data_processing': {{
        **DATA_PROCESSING_CONFIG,
        'outlier_quantiles': (0.01, 0.99) if '{config['type']}' == 'small' else (0.005, 0.995),
        'missing_strategy': 'interpolate',
        'smoothing_window': 3 if '{config['type']}' == 'small' else 5
    }},
    
    # 预测配置
    'prediction_config': {{
        'output_columns': OUTPUT_CONFIG['columns'],
        'time_format': OUTPUT_CONFIG['time_format'],
        'capacity_constraint': True,
        'min_output': 0.0,
        'max_output': {config['capacity_mw']}.0,
        'post_processing': {{
            'smooth_predictions': True,
            'smooth_window': 3 if '{config['type']}' == 'small' else 5
        }}
    }},
    
    # 错误处理配置
    'error_handling': {{
        **ERROR_HANDLING_CONFIG,
        'site_specific_fallbacks': {{
            'data_missing_threshold': 0.05,
            'model_performance_threshold': 0.8,
            'prediction_range_check': True
        }}
    }},
    
    # 日志配置
    'logging': {{
        **LOGGING_CONFIG,
        'log_file': f'logs/{site_id}_site.log',
        'detailed_logging': True
    }},
    
    # 验证配置
    'validation': {{
        'train_test_split': 0.2,
        'time_series_split': True,
        'validation_metrics': EVALUATION_METRICS,
        'early_stopping_rounds': 100
    }}
}}

# 导出配置
def get_{site_id}_config():
    """获取{site_id.upper()}站点配置"""
    # 确保模型目录存在
    {site_id.upper()}_CONFIG['data_paths']['model_dir'].mkdir(exist_ok=True)
    return {site_id.upper()}_CONFIG

if __name__ == "__main__":
    # 测试配置
    config = get_{site_id}_config()
    print("{site_id.upper()}站点配置加载成功")
    print(f"站点ID: {{config['site_info']['site_id']}}")
    print(f"装机容量: {{config['site_info']['capacity_mw']}}MW")
    print(f"模型目录: {{config['data_paths']['model_dir']}}")
'''
    
    return template

def replace_site_references(content: str, old_site: str, new_site: str) -> str:
    """替换文件内容中的站点引用"""
    replacements = [
        (old_site.lower(), new_site.lower()),
        (old_site.upper(), new_site.upper()),
        (f'{old_site.lower()}_', f'{new_site.lower()}_'),
        (f'{old_site.upper()}_', f'{new_site.upper()}_'),
        (f'sites/{old_site}', f'sites/{new_site}'),
        (f'models_{old_site}', f'models_{new_site}'),
        (f'{old_site}站点', f'{new_site}站点'),
        (f'{old_site.upper()}站点', f'{new_site.upper()}站点')
    ]
    
    result = content
    for old, new in replacements:
        result = result.replace(old, new)
    
    return result

def generate_site_files(site_id: str, template_site: str = 'f1'):
    """为指定站点生成所有文件"""
    print(f"开始生成{site_id}站点文件...")
    
    # 创建站点目录
    site_dir = Path(f'sites/{site_id}')
    site_dir.mkdir(exist_ok=True)
    
    template_dir = Path(f'sites/{template_site}')
    
    # 生成配置文件
    config_content = generate_site_config(site_id)
    with open(site_dir / f'config_{site_id}.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    print(f"  ✓ 配置文件: config_{site_id}.py")
    
    # 复制并修改其他文件
    files_to_copy = [
        f'data_processor_{template_site}.py',
        f'feature_engineer_{template_site}.py', 
        f'model_trainer_{template_site}.py',
        f'predictor_{template_site}.py',
        f'train_{template_site}.py',
        f'predict_{template_site}.py'
    ]
    
    for file_name in files_to_copy:
        template_file = template_dir / file_name
        if template_file.exists():
            # 读取模板文件
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换站点引用
            new_content = replace_site_references(content, template_site, site_id)
            
            # 生成新文件名
            new_file_name = file_name.replace(template_site, site_id)
            new_file_path = site_dir / new_file_name
            
            # 写入新文件
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  ✓ {new_file_name}")
    
    # 生成根目录入口脚本
    train_entry = f'''"""
{site_id.upper()}站点训练入口脚本
调用sites/{site_id}/train_{site_id}.py进行{site_id.upper()}站点训练
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入{site_id.upper()}站点训练模块
from sites.{site_id}.train_{site_id} import main as train_{site_id}_main

if __name__ == "__main__":
    print("开始{site_id.upper()}站点训练...")
    success = train_{site_id}_main()
    
    if success:
        print("{site_id.upper()}站点训练成功完成！")
    else:
        print("{site_id.upper()}站点训练失败！")
        sys.exit(1)
'''
    
    predict_entry = f'''"""
{site_id.upper()}站点预测入口脚本
调用sites/{site_id}/predict_{site_id}.py进行{site_id.upper()}站点预测
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入{site_id.upper()}站点预测模块
from sites.{site_id}.predict_{site_id} import main as predict_{site_id}_main

if __name__ == "__main__":
    print("开始{site_id.upper()}站点预测...")
    success = predict_{site_id}_main()
    
    if success:
        print("{site_id.upper()}站点预测成功完成！")
    else:
        print("{site_id.upper()}站点预测失败！")
        sys.exit(1)
'''
    
    # 写入入口脚本
    with open(f'训练_{site_id}.py', 'w', encoding='utf-8') as f:
        f.write(train_entry)
    
    with open(f'预测_{site_id}.py', 'w', encoding='utf-8') as f:
        f.write(predict_entry)
    
    print(f"  ✓ 训练_{site_id}.py")
    print(f"  ✓ 预测_{site_id}.py")
    
    print(f"{site_id}站点文件生成完成！")

def generate_all_sites():
    """生成所有站点文件"""
    sites = ['f2', 'f3', 'f4', 'f5']
    
    for site_id in sites:
        generate_site_files(site_id)
        print()

if __name__ == "__main__":
    generate_all_sites()
    print("所有站点文件生成完成！")
