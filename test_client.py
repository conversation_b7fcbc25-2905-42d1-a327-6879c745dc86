"""
测试修改后的API客户端
"""

import pandas as pd
from pathlib import Path
import subprocess
import sys

def create_test_data():
    """创建测试数据文件"""
    # 读取原始测试数据
    original_file = Path("data/test/测试集A_风电预测_气象变量.csv")
    
    if not original_file.exists():
        print(f"原始测试文件不存在: {original_file}")
        return None
    
    # 读取前100行作为测试数据
    df = pd.read_csv(original_file)
    test_df = df.head(100)
    
    # 保存测试数据
    test_file = "test_sample.csv"
    test_df.to_csv(test_file, index=False)
    
    print(f"创建测试数据文件: {test_file}")
    print(f"  数据量: {len(test_df)}条记录")
    print(f"  站点: {sorted(test_df['站点编号'].unique())}")
    
    return test_file

def test_api_client():
    """测试API客户端"""
    
    # 创建测试数据
    test_file = create_test_data()
    if not test_file:
        return
    
    # 输出文件
    output_file = "test_predictions.csv"
    
    # 构建命令
    cmd = [
        sys.executable,  # python.exe路径
        "api请求.py",
        "--input", test_file,
        "--output", output_file,
        "--api-url", "http://localhost:8888"
    ]
    
    print(f"\n执行命令: {' '.join(cmd)}")
    
    try:
        # 执行客户端
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        print(f"\n返回码: {result.returncode}")
        print(f"标准输出:\n{result.stdout}")
        
        if result.stderr:
            print(f"标准错误:\n{result.stderr}")
        
        # 检查输出文件
        if Path(output_file).exists():
            output_df = pd.read_csv(output_file)
            print(f"\n输出文件生成成功:")
            print(f"  文件: {output_file}")
            print(f"  记录数: {len(output_df)}")
            print(f"  列: {list(output_df.columns)}")
            print(f"  前5行:")
            print(output_df.head())
        else:
            print(f"\n输出文件未生成: {output_file}")
            
    except Exception as e:
        print(f"执行失败: {str(e)}")

def test_health_check():
    """测试健康检查"""
    cmd = [
        sys.executable,
        "api请求.py",
        "--input", "dummy.csv",  # 虚拟文件，不会被使用
        "--output", "dummy_output.csv",
        "--check-only"
    ]
    
    print(f"执行健康检查: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        print(f"返回码: {result.returncode}")
        print(f"输出:\n{result.stdout}")
        if result.stderr:
            print(f"错误:\n{result.stderr}")
    except Exception as e:
        print(f"健康检查失败: {str(e)}")

if __name__ == "__main__":
    print("=== 测试API客户端 ===")
    
    print("\n1. 测试健康检查...")
    test_health_check()
    
    print("\n2. 测试完整预测流程...")
    test_api_client()
