"""
F2站点训练主脚本
执行F2站点的完整训练流程
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from sites.f2.config_f2 import get_f2_config
from sites.f2.data_processor_f2 import DataProcessorF2
from sites.f2.feature_engineer_f2 import FeatureEngineerF2
from sites.f2.model_trainer_f2 import ModelTrainerF2

import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/f2_train.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('F2_Train')

def main():
    """F2站点训练主函数"""
    logger = setup_logging()
    start_time = time.time()
    
    try:
        logger.info("="*50)
        logger.info("开始F2站点训练流程")
        logger.info("="*50)
        
        # 1. 加载配置
        config = get_f2_config()
        logger.info(f"站点信息: {config['site_info']['name']} ({config['site_info']['capacity_mw']}MW)")
        
        # 2. 数据处理
        logger.info("步骤1: 数据加载和处理")
        processor = DataProcessorF2()
        
        # 加载训练数据
        train_data = processor.load_data(str(config['data_paths']['train_data']), is_train=True)
        logger.info(f"训练数据加载完成: {train_data.shape}")
        
        # 数据清洗
        clean_data = processor.clean_data(train_data)
        logger.info(f"数据清洗完成: {clean_data.shape}")
        
        # 数据验证
        if not processor.validate_data(clean_data):
            raise ValueError("数据验证失败")
        logger.info("数据验证通过")
        
        # 3. 特征工程
        logger.info("步骤2: 特征工程")
        engineer = FeatureEngineerF2()
        
        # 特征提取
        features_data = engineer.extract_features(clean_data)
        logger.info(f"特征提取完成: {features_data.shape}")
        
        # 特征选择
        target = clean_data['出力(MW)']
        selected_data = engineer.select_features(features_data, target)
        logger.info(f"特征选择完成: {selected_data.shape}")
        
        # 准备训练数据
        feature_cols = engineer.get_feature_names()
        X = selected_data[feature_cols].fillna(0)
        y = target
        
        logger.info(f"最终训练数据: X{X.shape}, y{y.shape}")
        logger.info(f"特征数量: {len(feature_cols)}")
        
        # 4. 模型训练
        logger.info("步骤3: 模型训练")
        trainer = ModelTrainerF2()
        
        # 超参数优化
        logger.info("开始超参数优化...")
        best_params = trainer.optimize_hyperparameters(X, y)
        logger.info(f"超参数优化完成: {best_params}")
        
        # 交叉验证
        logger.info("开始交叉验证...")
        cv_results = trainer.cross_validate(X, y, best_params)
        logger.info(f"交叉验证完成: {cv_results}")
        
        # 训练最终模型
        logger.info("训练最终模型...")
        model = trainer.train_model(X, y, best_params)
        logger.info("模型训练完成")
        
        # 5. 模型评估
        logger.info("步骤4: 模型评估")
        metrics = trainer.evaluate_model(X, y)
        logger.info("模型评估完成:")
        for metric, value in metrics.items():
            logger.info(f"  {metric.upper()}: {value:.4f}")
        
        # 6. 特征重要性分析
        logger.info("步骤5: 特征重要性分析")
        importance_df = trainer.get_feature_importance(feature_cols)
        logger.info("Top 10 重要特征:")
        for idx, row in importance_df.head(10).iterrows():
            logger.info(f"  {row['feature']}: {row['importance']:.2f}")
        
        # 保存特征重要性
        importance_path = config['data_paths']['model_dir'] / 'f2_feature_importance.csv'
        importance_df.to_csv(importance_path, index=False, encoding='utf-8')
        logger.info(f"特征重要性保存: {importance_path}")
        
        # 7. 保存模型和相关文件
        logger.info("步骤6: 保存模型")
        trainer.save_model()
        
        # 保存特征名称
        feature_names_path = config['data_paths']['model_dir'] / 'f2_feature_names.txt'
        with open(feature_names_path, 'w', encoding='utf-8') as f:
            for feature in feature_cols:
                f.write(f"{feature}\n")
        logger.info(f"特征名称保存: {feature_names_path}")
        
        # 保存训练报告
        report_path = config['data_paths']['model_dir'] / 'f2_training_report.txt'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("F2站点训练报告\n")
            f.write("="*30 + "\n")
            f.write(f"训练时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据量: {len(X)}条\n")
            f.write(f"特征数: {len(feature_cols)}个\n")
            f.write(f"最佳参数: {best_params}\n")
            f.write("\n交叉验证结果:\n")
            for metric, value in cv_results.items():
                f.write(f"  {metric}: {value:.4f}\n")
            f.write("\n最终评估结果:\n")
            for metric, value in metrics.items():
                f.write(f"  {metric}: {value:.4f}\n")
        
        logger.info(f"训练报告保存: {report_path}")
        
        # 8. 训练完成
        elapsed_time = time.time() - start_time
        logger.info("="*50)
        logger.info(f"F2站点训练流程完成！耗时: {elapsed_time:.2f}秒")
        logger.info(f"模型保存路径: {config['data_paths']['model_dir']}")
        logger.info("="*50)
        
        return True
        
    except Exception as e:
        logger.error(f"F2站点训练失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("F2站点训练成功完成！")
        exit(0)
    else:
        print("F2站点训练失败！")
        exit(1)
