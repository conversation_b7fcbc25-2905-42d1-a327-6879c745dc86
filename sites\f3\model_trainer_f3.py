"""
F3站点模型训练器
专门为F3站点（48MW小型风电场）设计的模型训练
使用LightGBM + Optuna进行超参数优化
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import joblib
from typing import Dict, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.base_classes import BaseModelTrainer
from sites.f3.config_f3 import get_f3_config

# 机器学习库
import lightgbm as lgb
import optuna
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score


class ModelTrainerF3(BaseModelTrainer):
    """F3站点模型训练器"""
    
    def __init__(self):
        super().__init__('f3')
        self.config = get_f3_config()
        self.model_config = self.config['model_config']
        self.site_info = self.config['site_info']
        
    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """
        使用Optuna优化超参数
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            最优超参数
        """
        try:
            self.logger.info("开始F3站点超参数优化")
            
            # 创建Optuna study
            study = optuna.create_study(
                direction='minimize',
                study_name=f'f3_lgb_optimization',
                load_if_exists=True
            )
            
            # 定义目标函数
            def objective(trial):
                return self._objective_function(trial, X, y)
            
            # 执行优化
            study.optimize(
                objective, 
                n_trials=self.model_config['optuna_config']['n_trials'],
                timeout=self.model_config['optuna_config']['timeout']
            )
            
            self.best_params = study.best_params
            self.logger.info(f"超参数优化完成，最佳分数: {study.best_value:.4f}")
            self.logger.info(f"最佳参数: {self.best_params}")
            
            return self.best_params
            
        except Exception as e:
            self.logger.error(f"超参数优化失败: {str(e)}")
            # 返回默认参数
            return self._get_default_params()
    
    def _objective_function(self, trial, X: pd.DataFrame, y: pd.Series) -> float:
        """Optuna目标函数"""
        # 获取超参数搜索空间
        space = self.model_config['hyperparameter_space']
        
        # 建议超参数
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'verbose': -1,
            'random_state': 42,
            'n_jobs': -1,
            
            # 优化的超参数
            'num_leaves': trial.suggest_int('num_leaves', *space['num_leaves']),
            'learning_rate': trial.suggest_float('learning_rate', *space['learning_rate']),
            'feature_fraction': trial.suggest_float('feature_fraction', *space['feature_fraction']),
            'bagging_fraction': trial.suggest_float('bagging_fraction', *space['bagging_fraction']),
            'bagging_freq': trial.suggest_int('bagging_freq', *space['bagging_freq']),
            'min_child_samples': trial.suggest_int('min_child_samples', *space['min_child_samples']),
            'reg_alpha': trial.suggest_float('reg_alpha', *space['reg_alpha']),
            'reg_lambda': trial.suggest_float('reg_lambda', *space['reg_lambda'])
        }
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=self.model_config['optuna_config']['cv_folds'])
        scores = []
        
        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # 训练模型
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=1000,
                callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
            )
            
            # 预测和评估
            y_pred = model.predict(X_val)
            score = np.sqrt(mean_squared_error(y_val, y_pred))
            scores.append(score)
        
        return np.mean(scores)
    
    def _get_default_params(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            **self.model_config['lgb_base_params'],
            'num_leaves': 31,
            'learning_rate': 0.1,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'min_child_samples': 20,
            'reg_alpha': 0.0,
            'reg_lambda': 0.0
        }
    
    def train_model(self, X: pd.DataFrame, y: pd.Series, params: Dict[str, Any]) -> Any:
        """
        训练模型
        
        Args:
            X: 特征数据
            y: 目标变量
            params: 模型参数
            
        Returns:
            训练好的模型
        """
        try:
            self.logger.info("开始F3站点模型训练")
            
            # 数据分割
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
            y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
            
            # 创建LightGBM数据集
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            # 训练模型
            self.model = lgb.train(
                params,
                train_data,
                valid_sets=[train_data, val_data],
                valid_names=['train', 'valid'],
                num_boost_round=2000,
                callbacks=[
                    lgb.early_stopping(self.config['validation']['early_stopping_rounds']),
                    lgb.log_evaluation(100)
                ]
            )
            
            self.logger.info("F3站点模型训练完成")
            return self.model
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {str(e)}")
            raise
    
    def evaluate_model(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        评估模型
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            评估指标
        """
        try:
            if self.model is None:
                raise ValueError("模型未训练")
            
            # 预测
            y_pred = self.model.predict(X)
            
            # 计算评估指标
            metrics = {
                'rmse': np.sqrt(mean_squared_error(y, y_pred)),
                'mae': mean_absolute_error(y, y_pred),
                'mape': np.mean(np.abs((y - y_pred) / (y + 1e-8))) * 100,
                'r2': r2_score(y, y_pred)
            }
            
            self.logger.info("模型评估完成:")
            for metric, value in metrics.items():
                self.logger.info(f"  {metric.upper()}: {value:.4f}")
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {str(e)}")
            raise
    
    def save_model(self, model_path: str = None) -> None:
        """
        保存模型
        
        Args:
            model_path: 模型保存路径
        """
        try:
            if self.model is None:
                raise ValueError("模型未训练")
            
            if model_path is None:
                model_dir = self.config['data_paths']['model_dir']
                model_path = model_dir / 'f3_lgb_model.pkl'
            
            # 确保目录存在
            Path(model_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存模型
            joblib.dump(self.model, model_path)
            
            # 保存参数
            params_path = Path(model_path).parent / 'f3_model_params.pkl'
            joblib.dump(self.best_params, params_path)
            
            self.logger.info(f"模型保存成功: {model_path}")
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {str(e)}")
            raise
    
    def get_feature_importance(self, feature_names: list) -> pd.DataFrame:
        """
        获取特征重要性
        
        Args:
            feature_names: 特征名称列表
            
        Returns:
            特征重要性DataFrame
        """
        if self.model is None:
            raise ValueError("模型未训练")
        
        importance = self.model.feature_importance(importance_type='gain')
        
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        return importance_df
    
    def cross_validate(self, X: pd.DataFrame, y: pd.Series, params: Dict[str, Any]) -> Dict[str, float]:
        """
        交叉验证
        
        Args:
            X: 特征数据
            y: 目标变量
            params: 模型参数
            
        Returns:
            交叉验证结果
        """
        try:
            self.logger.info("开始交叉验证")
            
            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=5)
            scores = {'rmse': [], 'mae': [], 'r2': []}
            
            for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # 训练模型
                train_data = lgb.Dataset(X_train, label=y_train)
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                
                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=1000,
                    callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
                )
                
                # 预测和评估
                y_pred = model.predict(X_val)
                
                scores['rmse'].append(np.sqrt(mean_squared_error(y_val, y_pred)))
                scores['mae'].append(mean_absolute_error(y_val, y_pred))
                scores['r2'].append(r2_score(y_val, y_pred))
                
                self.logger.info(f"Fold {fold+1} - RMSE: {scores['rmse'][-1]:.4f}")
            
            # 计算平均值和标准差
            cv_results = {}
            for metric in scores:
                cv_results[f'{metric}_mean'] = np.mean(scores[metric])
                cv_results[f'{metric}_std'] = np.std(scores[metric])
            
            self.logger.info("交叉验证完成:")
            for metric, value in cv_results.items():
                self.logger.info(f"  {metric}: {value:.4f}")
            
            return cv_results
            
        except Exception as e:
            self.logger.error(f"交叉验证失败: {str(e)}")
            raise


if __name__ == "__main__":
    # 测试模型训练器
    try:
        from sites.f3.data_processor_f3 import DataProcessorF3
        from sites.f3.feature_engineer_f3 import FeatureEngineerF3
        
        print("开始测试F3站点模型训练器...")
        
        # 加载和处理数据
        processor = DataProcessorF3()
        config = get_f3_config()
        train_data = processor.load_data(str(config['data_paths']['train_data']), is_train=True)
        clean_data = processor.clean_data(train_data)
        
        # 特征工程
        engineer = FeatureEngineerF3()
        features_data = engineer.extract_features(clean_data)
        target = clean_data['出力(MW)']
        selected_data = engineer.select_features(features_data, target)
        
        # 准备训练数据
        feature_cols = engineer.get_feature_names()
        X = selected_data[feature_cols]
        y = target
        
        print(f"训练数据准备完成: X{X.shape}, y{y.shape}")
        
        # 模型训练
        trainer = ModelTrainerF3()
        
        # 超参数优化（使用少量试验进行测试）
        trainer.model_config['optuna_config']['n_trials'] = 5
        best_params = trainer.optimize_hyperparameters(X, y)
        print(f"超参数优化完成: {best_params}")
        
        # 训练模型
        model = trainer.train_model(X, y, best_params)
        print("模型训练完成")
        
        # 评估模型
        metrics = trainer.evaluate_model(X, y)
        print(f"模型评估完成: {metrics}")
        
        # 保存模型
        trainer.save_model()
        print("模型保存完成")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
