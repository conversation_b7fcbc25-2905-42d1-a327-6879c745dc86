#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
计算分数脚本
将真实答案与预测结果进行对比，计算每个站点的RMSE和总体平均RMSE
"""

import pandas as pd
import numpy as np
from pathlib import Path
import glob
from datetime import datetime
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def load_data_with_encoding(file_path, encodings=['gbk', 'utf-8', 'gb2312', 'utf-8-sig']):
    """尝试不同编码读取数据"""
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取: {file_path}")
            return df
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码时出错: {e}")
            continue
    
    raise ValueError(f"无法使用任何编码读取数据文件: {file_path}")

def get_submission_file(submissions_dir='submissions', filename='submission.csv'):
    """获取指定的提交文件"""
    submissions_path = Path(submissions_dir)

    if not submissions_path.exists():
        raise FileNotFoundError(f"submissions文件夹不存在: {submissions_path}")

    # 查找指定的提交文件
    submission_file = submissions_path / filename

    if not submission_file.exists():
        raise FileNotFoundError(f"提交文件不存在: {submission_file}")

    print(f"找到提交文件: {submission_file}")
    return submission_file

def standardize_column_names(df):
    """标准化列名"""
    # 创建列名映射
    column_mapping = {}
    
    for col in df.columns:
        if '站点' in col or 'site' in col.lower():
            column_mapping[col] = '站点编号'
        elif '时间' in col or 'time' in col.lower():
            column_mapping[col] = '时间'
        elif '出力' in col or 'power' in col.lower() or 'MW' in col:
            column_mapping[col] = '出力(MW)'
    
    # 重命名列
    df_renamed = df.rename(columns=column_mapping)
    
    # 检查必需列是否存在
    required_cols = ['站点编号', '时间', '出力(MW)']
    missing_cols = [col for col in required_cols if col not in df_renamed.columns]
    
    if missing_cols:
        print(f"警告: 缺少列 {missing_cols}")
        print(f"可用列: {list(df_renamed.columns)}")
        
        # 尝试自动匹配
        if len(df_renamed.columns) >= 3:
            df_renamed.columns = required_cols[:len(df_renamed.columns)]
            print(f"自动重命名列为: {list(df_renamed.columns)}")
    
    return df_renamed

def preprocess_data(df, data_name):
    """预处理数据"""
    print(f"\n预处理 {data_name} 数据...")
    
    # 标准化列名
    df = standardize_column_names(df)
    
    # 转换时间格式
    df['时间'] = pd.to_datetime(df['时间'])
    
    # 确保出力为数值类型
    df['出力(MW)'] = pd.to_numeric(df['出力(MW)'], errors='coerce')
    
    # 移除缺失值
    before_rows = len(df)
    df = df.dropna()
    after_rows = len(df)
    
    if before_rows != after_rows:
        print(f"移除了 {before_rows - after_rows} 行缺失数据")
    
    print(f"{data_name} 数据形状: {df.shape}")
    print(f"站点: {sorted(df['站点编号'].unique())}")
    print(f"时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
    print(f"出力范围: {df['出力(MW)'].min():.2f} - {df['出力(MW)'].max():.2f} MW")
    
    return df

def match_data(true_df, pred_df):
    """匹配真实值和预测值数据"""
    print("\n匹配真实值和预测值数据...")
    
    # 合并数据
    merged_df = pd.merge(
        true_df, pred_df,
        on=['站点编号', '时间'],
        how='inner',
        suffixes=('_true', '_pred')
    )
    
    print(f"匹配成功的数据点: {len(merged_df)}")
    
    if len(merged_df) == 0:
        print("警告: 没有匹配的数据点!")
        print("真实数据站点:", sorted(true_df['站点编号'].unique()))
        print("预测数据站点:", sorted(pred_df['站点编号'].unique()))
        print("真实数据时间范围:", true_df['时间'].min(), "到", true_df['时间'].max())
        print("预测数据时间范围:", pred_df['时间'].min(), "到", pred_df['时间'].max())
        return None
    
    # 检查各站点匹配情况
    site_counts = merged_df['站点编号'].value_counts().sort_index()
    print("各站点匹配数据量:")
    for site, count in site_counts.items():
        print(f"  {site}: {count} 条")
    
    return merged_df

def calculate_score(rmse):
    """计算Score = 1/(1+RMSE)"""
    return 1 / (1 + rmse)

def calculate_metrics(merged_df):
    """计算评估指标"""
    print("\n计算评估指标...")

    results = {}

    # 计算各站点的指标
    for site in sorted(merged_df['站点编号'].unique()):
        site_data = merged_df[merged_df['站点编号'] == site]

        true_values = site_data['出力(MW)_true'].values
        pred_values = site_data['出力(MW)_pred'].values

        # 计算RMSE
        rmse = np.sqrt(mean_squared_error(true_values, pred_values))

        # 计算Score
        score = calculate_score(rmse)

        # 计算MAE
        mae = mean_absolute_error(true_values, pred_values)

        # 计算MAPE
        mape = np.mean(np.abs((true_values - pred_values) / (true_values + 1e-8))) * 100

        # 计算R²
        ss_res = np.sum((true_values - pred_values) ** 2)
        ss_tot = np.sum((true_values - np.mean(true_values)) ** 2)
        r2 = 1 - (ss_res / (ss_tot + 1e-8))

        results[site] = {
            'count': len(site_data),
            'rmse': rmse,
            'score': score,
            'mae': mae,
            'mape': mape,
            'r2': r2,
            'true_mean': np.mean(true_values),
            'pred_mean': np.mean(pred_values),
            'true_std': np.std(true_values),
            'pred_std': np.std(pred_values)
        }

    # 计算总体指标
    all_true = merged_df['出力(MW)_true'].values
    all_pred = merged_df['出力(MW)_pred'].values

    overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
    overall_score = calculate_score(overall_rmse)
    overall_mae = mean_absolute_error(all_true, all_pred)
    overall_mape = np.mean(np.abs((all_true - all_pred) / (all_true + 1e-8))) * 100

    ss_res = np.sum((all_true - all_pred) ** 2)
    ss_tot = np.sum((all_true - np.mean(all_true)) ** 2)
    overall_r2 = 1 - (ss_res / (ss_tot + 1e-8))

    results['overall'] = {
        'count': len(merged_df),
        'rmse': overall_rmse,
        'score': overall_score,
        'mae': overall_mae,
        'mape': overall_mape,
        'r2': overall_r2,
        'true_mean': np.mean(all_true),
        'pred_mean': np.mean(all_pred),
        'true_std': np.std(all_true),
        'pred_std': np.std(all_pred)
    }

    return results

def print_results(results):
    """打印评估结果"""
    print("\n" + "="*80)
    print("评估结果")
    print("="*80)

    # 打印各站点结果
    print("\n各站点评估指标:")
    print("-" * 90)
    print(f"{'站点':<6} {'数据量':<8} {'RMSE':<10} {'Score':<10} {'MAE':<10} {'MAPE(%)':<10} {'R²':<10}")
    print("-" * 90)

    site_rmses = []
    site_scores = []
    for site in sorted([k for k in results.keys() if k != 'overall']):
        metrics = results[site]
        print(f"{site:<6} {metrics['count']:<8} {metrics['rmse']:<10.4f} "
              f"{metrics['score']:<10.6f} {metrics['mae']:<10.4f} {metrics['mape']:<10.2f} {metrics['r2']:<10.4f}")
        site_rmses.append(metrics['rmse'])
        site_scores.append(metrics['score'])

    print("-" * 90)

    # 计算站点平均RMSE和Score
    avg_site_rmse = np.mean(site_rmses)
    avg_site_score = calculate_score(avg_site_rmse)  # 基于平均RMSE计算Score

    print(f"5个站点平均RMSE: {avg_site_rmse:.4f}")
    print(f"基于平均RMSE的Score: {avg_site_score:.6f}")

    # 打印总体结果
    overall = results['overall']
    print(f"\n总体评估指标:")
    print(f"  总数据量: {overall['count']}")
    print(f"  总体RMSE: {overall['rmse']:.4f}")
    print(f"  总体Score: {overall['score']:.6f}")
    print(f"  总体MAE: {overall['mae']:.4f}")
    print(f"  总体MAPE: {overall['mape']:.2f}%")
    print(f"  总体R²: {overall['r2']:.4f}")

    # 打印统计对比
    print(f"\n预测vs真实值统计对比:")
    print("-" * 50)
    print(f"{'指标':<15} {'真实值':<15} {'预测值':<15}")
    print("-" * 50)
    print(f"{'平均值':<15} {overall['true_mean']:<15.4f} {overall['pred_mean']:<15.4f}")
    print(f"{'标准差':<15} {overall['true_std']:<15.4f} {overall['pred_std']:<15.4f}")

    return avg_site_rmse, overall['rmse'], avg_site_score, overall['score']

def save_results(results, output_file='evaluation_results.txt'):
    """保存评估结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"风电出力预测评估结果\n")
        f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*80 + "\n\n")
        
        # 各站点结果
        f.write("各站点评估指标:\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'站点':<6} {'数据量':<8} {'RMSE':<10} {'MAE':<10} {'MAPE(%)':<10} {'R²':<10}\n")
        f.write("-" * 80 + "\n")
        
        site_rmses = []
        for site in sorted([k for k in results.keys() if k != 'overall']):
            metrics = results[site]
            f.write(f"{site:<6} {metrics['count']:<8} {metrics['rmse']:<10.4f} "
                   f"{metrics['mae']:<10.4f} {metrics['mape']:<10.2f} {metrics['r2']:<10.4f}\n")
            site_rmses.append(metrics['rmse'])
        
        f.write("-" * 80 + "\n")
        
        # 平均RMSE
        avg_site_rmse = np.mean(site_rmses)
        f.write(f"5个站点平均RMSE: {avg_site_rmse:.4f}\n")
        
        # 总体结果
        overall = results['overall']
        f.write(f"\n总体评估指标:\n")
        f.write(f"  总数据量: {overall['count']}\n")
        f.write(f"  总体RMSE: {overall['rmse']:.4f}\n")
        f.write(f"  总体MAE: {overall['mae']:.4f}\n")
        f.write(f"  总体MAPE: {overall['mape']:.2f}%\n")
        f.write(f"  总体R²: {overall['r2']:.4f}\n")
    
    print(f"\n评估结果已保存到: {output_file}")

def main():
    """主函数"""
    print("="*60)
    print("风电出力预测评估")
    print("="*60)
    
    try:
        # 1. 加载真实答案
        print("\n步骤1: 加载真实答案数据")
        answer_file = "data/2023年2月答案.csv"
        
        if not Path(answer_file).exists():
            raise FileNotFoundError(f"答案文件不存在: {answer_file}")
        
        true_df = load_data_with_encoding(answer_file)
        true_df = preprocess_data(true_df, "真实答案")
        
        # 2. 加载预测结果
        print("\n步骤2: 加载预测结果")
        submission_file = get_submission_file()

        pred_df = load_data_with_encoding(submission_file)
        pred_df = preprocess_data(pred_df, "预测结果")
        
        # 3. 匹配数据
        print("\n步骤3: 匹配数据")
        merged_df = match_data(true_df, pred_df)
        
        if merged_df is None:
            return 1
        
        # 4. 计算评估指标
        print("\n步骤4: 计算评估指标")
        results = calculate_metrics(merged_df)
        
        # 5. 打印结果
        avg_site_rmse, overall_rmse, avg_site_score, overall_score = print_results(results)
        
        # 6. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"evaluation_results_{timestamp}.txt"
        save_results(results, output_file)
        
        print("\n" + "="*60)
        print("评估完成!")
        print(f"关键指标:")
        print(f"  5个站点平均RMSE: {avg_site_rmse:.4f}")
        print(f"  基于平均RMSE的Score: {avg_site_score:.6f}")
        print(f"  总体RMSE: {overall_rmse:.4f}")
        print(f"  总体Score: {overall_score:.6f}")
        print("="*60)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
