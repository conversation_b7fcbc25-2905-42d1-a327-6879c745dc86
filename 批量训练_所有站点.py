"""
批量训练所有站点
一键训练F1-F5所有站点的模型
"""

import sys
import time
import os
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入各站点训练模块
from sites.f1.train_f1 import main as train_f1_main
from sites.f2.train_f2 import main as train_f2_main
from sites.f3.train_f3 import main as train_f3_main
from sites.f4.train_f4 import main as train_f4_main
from sites.f5.train_f5 import main as train_f5_main

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    if Path(file_path).exists():
        size_bytes = Path(file_path).stat().st_size
        return size_bytes / (1024 * 1024)
    return 0

def check_model_files():
    """检查模型文件是否存在"""
    models_info = {}
    sites = ['f1', 'f2', 'f3', 'f4', 'f5']
    
    for site_id in sites:
        model_dir = Path(f'models/models_{site_id}')
        model_file = model_dir / f'{site_id}_lgb_model.pkl'
        
        if model_file.exists():
            size_mb = get_file_size_mb(model_file)
            mtime = datetime.fromtimestamp(model_file.stat().st_mtime)
            models_info[site_id] = {
                'exists': True,
                'size_mb': size_mb,
                'modified_time': mtime
            }
        else:
            models_info[site_id] = {'exists': False}
    
    return models_info

def print_training_summary(results):
    """打印训练总结"""
    print("\n" + "="*60)
    print("批量训练完成总结:")
    print("="*60)
    
    success_count = sum(1 for result in results.values() if result['status'] == 'success')
    failed_count = len(results) - success_count
    total_time = sum(result['duration'] for result in results.values())
    
    for site_id, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"{status_icon} {site_id.upper()}站点: {result['status']} (耗时: {result['duration']:.2f}秒)")
        if result['status'] == 'failed':
            print(f"   错误: {result['error']}")
    
    print(f"\n总计: {success_count}个成功, {failed_count}个失败")
    print(f"总耗时: {total_time:.2f}秒")
    
    # 检查生成的模型文件
    print(f"\n生成的模型文件:")
    models_info = check_model_files()
    for site_id, info in models_info.items():
        if info['exists']:
            print(f"✅ models/models_{site_id}/{site_id}_lgb_model.pkl ({info['size_mb']:.1f}MB, {info['modified_time'].strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print(f"❌ models/models_{site_id}/{site_id}_lgb_model.pkl (不存在)")

def main():
    """主函数"""
    print("="*60)
    print("开始批量训练所有站点...")
    print("="*60)
    
    # 站点训练函数映射
    train_functions = {
        'f1': train_f1_main,
        'f2': train_f2_main,
        'f3': train_f3_main,
        'f4': train_f4_main,
        'f5': train_f5_main
    }
    
    # 站点信息
    site_info = {
        'f1': 'f1风电场 (48MW)',
        'f2': 'f2风电场 (280MW)',
        'f3': 'f3风电场 (48MW)',
        'f4': 'f4风电场 (88MW)',
        'f5': 'f5风电场 (48MW)'
    }
    
    results = {}
    
    # 检查训练前的模型状态
    print("\n训练前模型状态:")
    models_before = check_model_files()
    for site_id, info in models_before.items():
        if info['exists']:
            print(f"  {site_id.upper()}: 已存在 ({info['size_mb']:.1f}MB, {info['modified_time'].strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print(f"  {site_id.upper()}: 不存在")
    
    # 逐个训练站点
    for i, (site_id, train_func) in enumerate(train_functions.items(), 1):
        print(f"\n[{i}/5] 开始训练{site_id.upper()}站点...")
        print(f"站点信息: {site_info[site_id]}")
        
        start_time = time.time()
        
        try:
            # 调用站点训练函数
            train_func()
            
            duration = time.time() - start_time
            results[site_id] = {
                'status': 'success',
                'duration': duration,
                'error': None
            }
            print(f"✅ {site_id.upper()}站点训练成功完成！耗时: {duration:.2f}秒")
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = str(e)
            results[site_id] = {
                'status': 'failed',
                'duration': duration,
                'error': error_msg
            }
            print(f"❌ {site_id.upper()}站点训练失败！耗时: {duration:.2f}秒")
            print(f"错误信息: {error_msg}")
            
            # 询问是否继续
            try:
                continue_training = input(f"\n是否继续训练其他站点？(y/n): ").lower().strip()
                if continue_training != 'y':
                    print("用户选择停止训练")
                    break
            except KeyboardInterrupt:
                print("\n用户中断训练")
                break
    
    # 打印训练总结
    print_training_summary(results)
    
    # 检查是否所有站点都训练成功
    success_sites = [site_id for site_id, result in results.items() if result['status'] == 'success']
    
    if len(success_sites) == 5:
        print(f"\n🎉 所有站点训练成功完成！")
        print(f"现在可以运行批量预测脚本: python 批量预测_所有站点.py")
    elif len(success_sites) > 0:
        print(f"\n⚠️  部分站点训练完成，成功的站点: {', '.join([s.upper() for s in success_sites])}")
        print(f"可以对成功训练的站点进行预测")
    else:
        print(f"\n❌ 所有站点训练都失败了，请检查错误信息并重试")
    
    return results

if __name__ == "__main__":
    try:
        results = main()
    except KeyboardInterrupt:
        print("\n\n用户中断了批量训练过程")
    except Exception as e:
        print(f"\n\n批量训练过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
