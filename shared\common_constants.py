"""
通用常量定义
包含项目中使用的所有常量
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 数据路径
DATA_PATHS = {
    'train': PROJECT_ROOT / 'data' / 'train' / '训练集_风电预测_气象变量及实际功率数据_截止1月.csv',
    'test': PROJECT_ROOT / 'data' / 'test' / '测试集_风电预测_气象变量及实际功率数据_2023年2月.csv',
    'submit_example': PROJECT_ROOT / 'data' / 'a_submit_example.csv'
}

# 站点信息
SITE_INFO = {
    'f1': {
        'name': 'f1风电场',
        'capacity_mw': 48,
        'type': 'small'  # 小型站点
    },
    'f2': {
        'name': 'f2风电场', 
        'capacity_mw': 280,
        'type': 'large'  # 大型站点
    },
    'f3': {
        'name': 'f3风电场',
        'capacity_mw': 48,
        'type': 'small'  # 小型站点
    },
    'f4': {
        'name': 'f4风电场',
        'capacity_mw': 88,
        'type': 'medium'  # 中型站点
    },
    'f5': {
        'name': 'f5风电场',
        'capacity_mw': 48,
        'type': 'small'  # 小型站点
    }
}

# 数据列名
COLUMN_NAMES = {
    'site_id': '站点编号',
    'timestamp': '时间',
    'target': '出力(MW)',
    'wind_speed': '10米风速（10m/s）',
    'temperature': '温度（K）',
    'pressure': '气压(Pa）',
    'humidity': '相对湿度（%）',
    'wind_direction': '10米风向（°)',
    'wind_speed_100m': '100m风速（100m/s）',
    'wind_direction_100m': '100m风向（°)',
    'cloud_cover': '云量',
    'radiation': '辐照强度（J/m2）',
    'precipitation': '降水（m）'
}

# 气象特征列表
WEATHER_FEATURES = [
    '10米风速（10m/s）',
    '温度（K）',
    '气压(Pa）',
    '相对湿度（%）',
    '10米风向（°)',
    '100m风速（100m/s）',
    '100m风向（°)',
    '云量',
    '辐照强度（J/m2）',
    '降水（m）'
]

# 时间特征配置
TIME_FEATURES = {
    'basic': ['hour', 'day_of_week', 'month', 'season'],
    'extended': ['hour', 'day_of_week', 'month', 'season', 'is_weekend', 'hour_sin', 'hour_cos']
}

# 滞后特征配置（小时）
LAG_FEATURES = {
    'small_site': [1, 2, 3, 6, 12],      # 小型站点：短期滞后
    'medium_site': [1, 2, 3, 6, 12, 24], # 中型站点：中期滞后
    'large_site': [1, 3, 6, 12, 24, 48]  # 大型站点：长期滞后
}

# 统计特征窗口配置（小时）
ROLLING_WINDOWS = {
    'small_site': [3, 6, 12],           # 小型站点：高频统计
    'medium_site': [3, 6, 12, 24],      # 中型站点：中频统计
    'large_site': [6, 12, 24, 48]       # 大型站点：低频统计
}

# 模型配置
MODEL_CONFIG = {
    'lgb_base_params': {
        'objective': 'regression',
        'metric': 'rmse',
        'boosting_type': 'gbdt',
        'verbose': -1,
        'random_state': 42,
        'n_jobs': -1
    },
    'optuna_config': {
        'n_trials': {
            'small_site': 50,   # 小型站点试验次数
            'medium_site': 75,  # 中型站点试验次数
            'large_site': 100   # 大型站点试验次数
        },
        'timeout': 3600,  # 1小时超时
        'cv_folds': 5
    }
}

# 超参数搜索空间
HYPERPARAMETER_SPACE = {
    'small_site': {
        'num_leaves': (15, 50),
        'learning_rate': (0.05, 0.2),
        'feature_fraction': (0.6, 0.9),
        'bagging_fraction': (0.6, 0.9),
        'bagging_freq': (1, 7),
        'min_child_samples': (10, 50),
        'reg_alpha': (0.0, 0.1),
        'reg_lambda': (0.0, 0.1)
    },
    'medium_site': {
        'num_leaves': (20, 75),
        'learning_rate': (0.03, 0.15),
        'feature_fraction': (0.65, 0.95),
        'bagging_fraction': (0.65, 0.95),
        'bagging_freq': (1, 7),
        'min_child_samples': (15, 75),
        'reg_alpha': (0.0, 0.3),
        'reg_lambda': (0.0, 0.3)
    },
    'large_site': {
        'num_leaves': (31, 100),
        'learning_rate': (0.01, 0.1),
        'feature_fraction': (0.7, 1.0),
        'bagging_fraction': (0.7, 1.0),
        'bagging_freq': (1, 7),
        'min_child_samples': (20, 100),
        'reg_alpha': (0.0, 0.5),
        'reg_lambda': (0.0, 0.5)
    }
}

# 文件路径配置
PATHS = {
    'models': PROJECT_ROOT / 'models',
    'submissions': PROJECT_ROOT / 'submissions',
    'logs': PROJECT_ROOT / 'logs',
    'config': PROJECT_ROOT / 'config',
    'sites': PROJECT_ROOT / 'sites'
}

# 确保目录存在
for path in PATHS.values():
    path.mkdir(exist_ok=True)

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'encoding': 'utf-8'
}

# 数据处理配置
DATA_PROCESSING_CONFIG = {
    'encoding': 'utf-8',
    'missing_value_threshold': 0.1,  # 缺失值阈值
    'outlier_method': 'iqr',         # 异常值处理方法
    'interpolation_method': 'linear'  # 插值方法
}

# 特征选择配置
FEATURE_SELECTION_CONFIG = {
    'correlation_threshold': 0.95,   # 相关性阈值
    'importance_threshold': 0.001,   # 重要性阈值
    'max_features': 100              # 最大特征数
}

# 评估指标
EVALUATION_METRICS = ['rmse', 'mae', 'mape', 'r2']

# 输出格式配置
OUTPUT_CONFIG = {
    'columns': ['站点编号', '时间', '出力(MW)'],
    'time_format': '%Y/%m/%d %H:%M',
    'encoding': 'utf-8'
}

# 季节映射
SEASON_MAPPING = {
    12: 'winter', 1: 'winter', 2: 'winter',
    3: 'spring', 4: 'spring', 5: 'spring',
    6: 'summer', 7: 'summer', 8: 'summer',
    9: 'autumn', 10: 'autumn', 11: 'autumn'
}

# 错误处理配置
ERROR_HANDLING_CONFIG = {
    'max_retries': 3,
    'retry_delay': 1,  # 秒
    'fallback_strategies': {
        'data_error': 'interpolate',
        'model_error': 'simple_model',
        'prediction_error': 'historical_average'
    }
}
