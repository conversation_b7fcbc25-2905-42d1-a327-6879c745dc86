"""
批量预测所有站点
一键生成F1-F5所有站点的预测结果
"""

import sys
import time
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入各站点预测模块
from sites.f1.predict_f1 import main as predict_f1_main
from sites.f2.predict_f2 import main as predict_f2_main
from sites.f3.predict_f3 import main as predict_f3_main
from sites.f4.predict_f4 import main as predict_f4_main
from sites.f5.predict_f5 import main as predict_f5_main

def merge_predictions_to_submit():
    """合并所有站点的预测结果到submit.csv文件"""
    print("  正在合并预测结果...")

    submissions_dir = Path('submissions')
    all_predictions = []

    # 站点列表
    sites = ['f1', 'f2', 'f3', 'f4', 'f5']

    for site_id in sites:
        prediction_file = submissions_dir / f'{site_id}_predictions.csv'

        if prediction_file.exists():
            try:
                # 读取站点预测结果
                df = pd.read_csv(prediction_file)
                all_predictions.append(df)
                print(f"  ✅ 读取{site_id.upper()}站点预测: {len(df)}条记录")
            except Exception as e:
                print(f"  ❌ 读取{site_id.upper()}站点预测失败: {str(e)}")
                raise
        else:
            print(f"  ❌ {site_id.upper()}站点预测文件不存在: {prediction_file}")
            raise FileNotFoundError(f"预测文件不存在: {prediction_file}")

    # 合并所有预测结果
    if all_predictions:
        merged_df = pd.concat(all_predictions, ignore_index=True)

        # 按站点编号和时间排序
        merged_df = merged_df.sort_values(['站点编号', '时间']).reset_index(drop=True)

        # 保存合并结果
        submit_file = submissions_dir / 'submission.csv'
        merged_df.to_csv(submit_file, index=False, encoding='utf-8-sig')

        print(f"  ✅ 合并完成: {len(merged_df)}条记录")
        print(f"  📁 保存路径: {submit_file}")

        # 显示合并结果统计
        site_counts = merged_df['站点编号'].value_counts().sort_index()
        print("  📊 各站点记录数:")
        for site, count in site_counts.items():
            print(f"    {site}: {count}条")

        # 显示时间范围
        time_range = merged_df['时间'].agg(['min', 'max'])
        print(f"  📅 时间范围: {time_range['min']} ~ {time_range['max']}")

        # 显示预测值统计
        power_stats = merged_df['出力(MW)'].describe()
        print(f"  ⚡ 出力统计: 均值={power_stats['mean']:.2f}MW, 最大={power_stats['max']:.2f}MW, 最小={power_stats['min']:.2f}MW")

        return submit_file
    else:
        raise ValueError("没有找到任何预测结果文件")

def main():
    """批量预测所有站点"""
    print("=" * 60)
    print("开始批量预测所有站点...")
    print("=" * 60)
    
    # 站点配置
    sites = [
        {'id': 'f1', 'name': 'F1站点', 'predict_func': predict_f1_main},
        {'id': 'f2', 'name': 'F2站点', 'predict_func': predict_f2_main},
        {'id': 'f3', 'name': 'F3站点', 'predict_func': predict_f3_main},
        {'id': 'f4', 'name': 'F4站点', 'predict_func': predict_f4_main},
        {'id': 'f5', 'name': 'F5站点', 'predict_func': predict_f5_main}
    ]
    
    results = {}
    total_start_time = time.time()
    
    for i, site in enumerate(sites, 1):
        print(f"\n[{i}/5] 开始预测{site['name']}...")
        start_time = time.time()
        
        try:
            success = site['predict_func']()
            end_time = time.time()
            duration = end_time - start_time
            
            if success:
                print(f"✅ {site['name']}预测成功完成！耗时: {duration:.2f}秒")
                results[site['id']] = {'status': 'success', 'duration': duration}
            else:
                print(f"❌ {site['name']}预测失败！")
                results[site['id']] = {'status': 'failed', 'duration': duration}
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ {site['name']}预测出错: {str(e)}")
            results[site['id']] = {'status': 'error', 'duration': duration, 'error': str(e)}
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 输出总结
    print("\n" + "=" * 60)
    print("批量预测完成总结:")
    print("=" * 60)
    
    success_count = 0
    failed_count = 0
    
    for site_id, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"{status_icon} {site_id.upper()}站点: {result['status']} (耗时: {result['duration']:.2f}秒)")
        
        if result['status'] == 'success':
            success_count += 1
        else:
            failed_count += 1
            if 'error' in result:
                print(f"   错误信息: {result['error']}")
    
    print(f"\n总计: {success_count}个成功, {failed_count}个失败")
    print(f"总耗时: {total_duration:.2f}秒")
    
    # 检查生成的文件
    print("\n生成的预测文件:")
    submissions_dir = Path('submissions')
    if submissions_dir.exists():
        for site_id in ['f1', 'f2', 'f3', 'f4', 'f5']:
            prediction_file = submissions_dir / f'{site_id}_predictions.csv'
            if prediction_file.exists():
                file_size = prediction_file.stat().st_size
                print(f"✅ {prediction_file} ({file_size} bytes)")
            else:
                print(f"❌ {prediction_file} (文件不存在)")
    
    # 如果所有站点预测成功，生成合并的submit.csv文件
    if failed_count == 0:
        print("\n生成合并的提交文件...")
        try:
            merge_predictions_to_submit()
            print("✅ submission.csv 文件生成成功！")
        except Exception as e:
            print(f"❌ 生成submission.csv失败: {str(e)}")
            failed_count += 1

    print("\n" + "=" * 60)
    if failed_count == 0:
        print("🎉 所有站点预测成功完成！")
    else:
        print(f"⚠️  {failed_count}个站点预测失败，请检查错误信息")
    print("=" * 60)

    return failed_count == 0

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
