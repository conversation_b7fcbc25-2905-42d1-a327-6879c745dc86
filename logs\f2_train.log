2025-07-23 20:16:51,049 - F2_Train - INFO - ==================================================
2025-07-23 20:16:51,050 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 20:16:51,050 - F2_Train - INFO - ==================================================
2025-07-23 20:16:51,050 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 20:16:51,051 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 20:16:51,051 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 20:16:51,301 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 20:16:51,320 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 20:16:51,337 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 20:16:51,341 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 20:16:51,343 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 20:16:51,343 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 20:16:51,359 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 20:16:51,362 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 20:16:51,364 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 20:16:51,366 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 20:16:51,369 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 20:16:51,371 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 20:16:51,377 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 20:16:51,377 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 20:16:51,377 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 20:16:51,377 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 20:16:51,382 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 20:16:51,383 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 20:16:51,383 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 20:16:51,383 - F2_Train - INFO - 数据验证通过
2025-07-23 20:16:51,383 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 20:16:51,384 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 20:16:51,398 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 10个
2025-07-23 20:16:51,412 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 20:16:51,421 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 20:16:51,895 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 20:16:51,897 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 20:16:51,905 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 20:16:52,277 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 192
2025-07-23 20:16:52,278 - F2_Train - INFO - 特征提取完成: (37821, 210)
2025-07-23 20:16:52,278 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 20:16:52,373 - FeatureEngineer_f2 - INFO - 相关性筛选: 192 -> 188
2025-07-23 20:17:31,499 - FeatureEngineer_f2 - INFO - 重要性筛选: 188 -> 94
2025-07-23 20:17:32,093 - FeatureEngineer_f2 - INFO - 高相关性筛选: 94 -> 44
2025-07-23 20:17:32,096 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 44
2025-07-23 20:17:32,105 - F2_Train - INFO - 特征选择完成: (37821, 46)
2025-07-23 20:17:32,128 - F2_Train - INFO - 最终训练数据: X(37821, 44), y(37821,)
2025-07-23 20:17:32,128 - F2_Train - INFO - 特征数量: 44
2025-07-23 20:17:32,128 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 20:17:32,130 - F2_Train - INFO - 开始超参数优化...
2025-07-23 20:17:32,130 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 20:21:35,608 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 42.9879
2025-07-23 20:21:35,608 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 32, 'learning_rate': 0.05164629942270082, 'feature_fraction': 0.729602846347331, 'bagging_fraction': 0.7649044224765087, 'bagging_freq': 6, 'min_child_samples': 65, 'reg_alpha': 0.027871024014788248, 'reg_lambda': 0.14858222680417912}
2025-07-23 20:21:35,609 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 32, 'learning_rate': 0.05164629942270082, 'feature_fraction': 0.729602846347331, 'bagging_fraction': 0.7649044224765087, 'bagging_freq': 6, 'min_child_samples': 65, 'reg_alpha': 0.027871024014788248, 'reg_lambda': 0.14858222680417912}
2025-07-23 20:21:35,609 - F2_Train - INFO - 开始交叉验证...
2025-07-23 20:21:35,609 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 20:21:35,862 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 43.2552
2025-07-23 20:21:36,081 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 51.4804
2025-07-23 20:21:36,327 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 31.1683
2025-07-23 20:21:36,795 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 48.7825
2025-07-23 20:21:37,224 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 38.8287
2025-07-23 20:21:37,225 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 20:21:37,225 - ModelTrainer_f2 - INFO -   rmse_mean: 42.7030
2025-07-23 20:21:37,225 - ModelTrainer_f2 - INFO -   rmse_std: 7.2439
2025-07-23 20:21:37,225 - ModelTrainer_f2 - INFO -   mae_mean: 31.9864
2025-07-23 20:21:37,226 - ModelTrainer_f2 - INFO -   mae_std: 6.9863
2025-07-23 20:21:37,226 - ModelTrainer_f2 - INFO -   r2_mean: 0.6676
2025-07-23 20:21:37,226 - ModelTrainer_f2 - INFO -   r2_std: 0.1211
2025-07-23 20:21:37,227 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(42.70302782358432), 'rmse_std': np.float64(7.2438744434977425), 'mae_mean': np.float64(31.986363771750952), 'mae_std': np.float64(6.986309175054457), 'r2_mean': np.float64(0.6675822346047182), 'r2_std': np.float64(0.12108490597425016)}
2025-07-23 20:21:37,227 - F2_Train - INFO - 训练最终模型...
2025-07-23 20:21:37,227 - ModelTrainer_f2 - INFO - 开始F2站点模型训练
2025-07-23 20:21:37,509 - ModelTrainer_f2 - INFO - F2站点模型训练完成
2025-07-23 20:21:37,509 - F2_Train - INFO - 模型训练完成
2025-07-23 20:21:37,509 - F2_Train - INFO - 步骤4: 模型评估
2025-07-23 20:21:37,526 - ModelTrainer_f2 - INFO - 模型评估完成:
2025-07-23 20:21:37,526 - ModelTrainer_f2 - INFO -   RMSE: 33.4434
2025-07-23 20:21:37,527 - ModelTrainer_f2 - INFO -   MAE: 25.2562
2025-07-23 20:21:37,527 - ModelTrainer_f2 - INFO -   MAPE: 2421598055.6848
2025-07-23 20:21:37,527 - ModelTrainer_f2 - INFO -   R2: 0.8718
2025-07-23 20:21:37,527 - F2_Train - INFO - 模型评估完成:
2025-07-23 20:21:37,527 - F2_Train - INFO -   RMSE: 33.4434
2025-07-23 20:21:37,527 - F2_Train - INFO -   MAE: 25.2562
2025-07-23 20:21:37,528 - F2_Train - INFO -   MAPE: 2421598055.6848
2025-07-23 20:21:37,528 - F2_Train - INFO -   R2: 0.8718
2025-07-23 20:21:37,528 - F2_Train - INFO - 步骤5: 特征重要性分析
2025-07-23 20:21:37,529 - F2_Train - INFO - Top 10 重要特征:
2025-07-23 20:21:37,529 - F2_Train - INFO -   10米风速（10m/s）_mean_6h: 1120825259.49
2025-07-23 20:21:37,529 - F2_Train - INFO -   10米风速（10m/s）_lag_24h: 228171045.38
2025-07-23 20:21:37,530 - F2_Train - INFO -   10米风速（10m/s）_lag_48h: 146760364.10
2025-07-23 20:21:37,530 - F2_Train - INFO -   气压(Pa）_lag_1h: 50865616.61
2025-07-23 20:21:37,530 - F2_Train - INFO -   wind_speed_ratio_100_10: 30081154.01
2025-07-23 20:21:37,530 - F2_Train - INFO -   day_of_year: 24052385.89
2025-07-23 20:21:37,530 - F2_Train - INFO -   wind_rsi: 18505358.70
2025-07-23 20:21:37,531 - F2_Train - INFO -   温度（K）_lag_24h: 17657116.11
2025-07-23 20:21:37,531 - F2_Train - INFO -   温度（K）_std_48h: 13570686.59
2025-07-23 20:21:37,532 - F2_Train - INFO -   10米风速（10m/s）_std_48h: 12197551.91
2025-07-23 20:21:37,535 - F2_Train - INFO - 特征重要性保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_feature_importance.csv
2025-07-23 20:21:37,535 - F2_Train - INFO - 步骤6: 保存模型
2025-07-23 20:21:37,548 - ModelTrainer_f2 - INFO - 模型保存成功: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_lgb_model.pkl
2025-07-23 20:21:37,549 - F2_Train - INFO - 特征名称保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_feature_names.txt
2025-07-23 20:21:37,550 - F2_Train - INFO - 训练报告保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_training_report.txt
2025-07-23 20:21:37,551 - F2_Train - INFO - ==================================================
2025-07-23 20:21:37,551 - F2_Train - INFO - F2站点训练流程完成！耗时: 286.50秒
2025-07-23 20:21:37,551 - F2_Train - INFO - 模型保存路径: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2
2025-07-23 20:21:37,551 - F2_Train - INFO - ==================================================
2025-07-23 20:55:01,826 - F2_Train - INFO - ==================================================
2025-07-23 20:55:01,826 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 20:55:01,826 - F2_Train - INFO - ==================================================
2025-07-23 20:55:01,827 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 20:55:01,827 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 20:55:01,828 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 20:55:02,091 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 20:55:02,102 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 20:55:02,120 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 20:55:02,124 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 20:55:02,126 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 20:55:02,127 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 20:55:02,144 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 20:55:02,148 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 20:55:02,151 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 20:55:02,153 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 20:55:02,156 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 20:55:02,158 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 20:55:02,165 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 20:55:02,165 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 20:55:02,165 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 20:55:02,165 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 20:55:02,170 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 20:55:02,171 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 20:55:02,171 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 20:55:02,171 - F2_Train - INFO - 数据验证通过
2025-07-23 20:55:02,172 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 20:55:02,172 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 20:55:02,196 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 19个
2025-07-23 20:55:08,749 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 20:55:08,757 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 20:55:09,219 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 20:55:09,228 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 20:55:09,235 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 20:55:09,256 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 20:55:09,275 - FeatureEngineer_f2 - INFO - 功率曲线特征提取完成
2025-07-23 20:55:09,708 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 232
2025-07-23 20:55:09,708 - F2_Train - INFO - 特征提取完成: (37821, 250)
2025-07-23 20:55:09,709 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 20:55:09,825 - FeatureEngineer_f2 - INFO - 相关性筛选: 232 -> 228
2025-07-23 20:55:53,921 - FeatureEngineer_f2 - INFO - 重要性筛选: 228 -> 114
2025-07-23 20:55:54,691 - FeatureEngineer_f2 - INFO - 高相关性筛选: 114 -> 54
2025-07-23 20:55:54,694 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 54
2025-07-23 20:55:54,702 - F2_Train - INFO - 特征选择完成: (37821, 56)
2025-07-23 20:55:54,721 - F2_Train - INFO - 最终训练数据: X(37821, 54), y(37821,)
2025-07-23 20:55:54,721 - F2_Train - INFO - 特征数量: 54
2025-07-23 20:55:54,721 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 20:55:54,722 - F2_Train - INFO - 开始超参数优化...
2025-07-23 20:55:54,722 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 21:01:55,232 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 44.0025
2025-07-23 21:01:55,232 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 31, 'learning_rate': 0.01620710684828079, 'feature_fraction': 0.7116142676646562, 'bagging_fraction': 0.9218716672093867, 'bagging_freq': 1, 'min_child_samples': 93, 'reg_alpha': 0.1451988313337167, 'reg_lambda': 0.2509436162685707}
2025-07-23 21:01:55,233 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 31, 'learning_rate': 0.01620710684828079, 'feature_fraction': 0.7116142676646562, 'bagging_fraction': 0.9218716672093867, 'bagging_freq': 1, 'min_child_samples': 93, 'reg_alpha': 0.1451988313337167, 'reg_lambda': 0.2509436162685707}
2025-07-23 21:01:55,233 - F2_Train - INFO - 开始交叉验证...
2025-07-23 21:01:55,233 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 21:01:55,668 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 45.5851
2025-07-23 21:01:56,118 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 51.9024
2025-07-23 21:01:56,646 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 31.4875
2025-07-23 21:01:57,437 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 55.9592
2025-07-23 21:01:58,738 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 37.6940
2025-07-23 21:01:58,739 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 21:01:58,739 - ModelTrainer_f2 - INFO -   rmse_mean: 44.5256
2025-07-23 21:01:58,739 - ModelTrainer_f2 - INFO -   rmse_std: 8.9770
2025-07-23 21:01:58,739 - ModelTrainer_f2 - INFO -   mae_mean: 33.6741
2025-07-23 21:01:58,740 - ModelTrainer_f2 - INFO -   mae_std: 8.6019
2025-07-23 21:01:58,740 - ModelTrainer_f2 - INFO -   r2_mean: 0.6424
2025-07-23 21:01:58,740 - ModelTrainer_f2 - INFO -   r2_std: 0.1224
2025-07-23 21:01:58,742 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(44.52564134308748), 'rmse_std': np.float64(8.976958221781537), 'mae_mean': np.float64(33.67410840331063), 'mae_std': np.float64(8.60194804956733), 'r2_mean': np.float64(0.6423926105179919), 'r2_std': np.float64(0.12239743968196588)}
2025-07-23 21:01:58,742 - F2_Train - INFO - 训练最终模型...
2025-07-23 21:01:58,742 - ModelTrainer_f2 - INFO - 开始F2站点模型训练
2025-07-23 21:01:59,405 - ModelTrainer_f2 - INFO - F2站点模型训练完成
2025-07-23 21:01:59,406 - F2_Train - INFO - 模型训练完成
2025-07-23 21:01:59,406 - F2_Train - INFO - 步骤4: 模型评估
2025-07-23 21:01:59,452 - ModelTrainer_f2 - INFO - 模型评估完成:
2025-07-23 21:01:59,452 - ModelTrainer_f2 - INFO -   RMSE: 31.6974
2025-07-23 21:01:59,452 - ModelTrainer_f2 - INFO -   MAE: 23.4557
2025-07-23 21:01:59,452 - ModelTrainer_f2 - INFO -   MAPE: 2188685237.9735
2025-07-23 21:01:59,453 - ModelTrainer_f2 - INFO -   R2: 0.8849
2025-07-23 21:01:59,453 - F2_Train - INFO - 模型评估完成:
2025-07-23 21:01:59,453 - F2_Train - INFO -   RMSE: 31.6974
2025-07-23 21:01:59,453 - F2_Train - INFO -   MAE: 23.4557
2025-07-23 21:01:59,453 - F2_Train - INFO -   MAPE: 2188685237.9735
2025-07-23 21:01:59,453 - F2_Train - INFO -   R2: 0.8849
2025-07-23 21:01:59,453 - F2_Train - INFO - 步骤5: 特征重要性分析
2025-07-23 21:01:59,454 - F2_Train - INFO - Top 10 重要特征:
2025-07-23 21:01:59,455 - F2_Train - INFO -   wind_power_density: 4379132930.59
2025-07-23 21:01:59,455 - F2_Train - INFO -   10米风速（10m/s）_lag_12h: 1069601772.00
2025-07-23 21:01:59,455 - F2_Train - INFO -   10米风速（10m/s）_lag_24h: 354564137.30
2025-07-23 21:01:59,456 - F2_Train - INFO -   wind_speed_ratio_100_10: 79547485.92
2025-07-23 21:01:59,456 - F2_Train - INFO -   wind_percentile_rank: 72788463.12
2025-07-23 21:01:59,456 - F2_Train - INFO -   day_of_year: 72733899.15
2025-07-23 21:01:59,456 - F2_Train - INFO -   气压(Pa）_lag_24h: 60817606.91
2025-07-23 21:01:59,457 - F2_Train - INFO -   温度（K）_lag_12h: 58089245.19
2025-07-23 21:01:59,457 - F2_Train - INFO -   10米风速（10m/s）_lag_48h: 57597297.49
2025-07-23 21:01:59,457 - F2_Train - INFO -   day_of_year_sin: 52508191.45
2025-07-23 21:01:59,460 - F2_Train - INFO - 特征重要性保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_feature_importance.csv
2025-07-23 21:01:59,461 - F2_Train - INFO - 步骤6: 保存模型
2025-07-23 21:01:59,469 - ModelTrainer_f2 - INFO - 模型保存成功: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_lgb_model.pkl
2025-07-23 21:01:59,470 - F2_Train - INFO - 特征名称保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_feature_names.txt
2025-07-23 21:01:59,471 - F2_Train - INFO - 训练报告保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_training_report.txt
2025-07-23 21:01:59,471 - F2_Train - INFO - ==================================================
2025-07-23 21:01:59,471 - F2_Train - INFO - F2站点训练流程完成！耗时: 417.65秒
2025-07-23 21:01:59,471 - F2_Train - INFO - 模型保存路径: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2
2025-07-23 21:01:59,472 - F2_Train - INFO - ==================================================
2025-07-23 21:25:10,783 - F2_Train - INFO - ==================================================
2025-07-23 21:25:10,783 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 21:25:10,783 - F2_Train - INFO - ==================================================
2025-07-23 21:25:10,784 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 21:25:10,784 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 21:25:10,785 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 21:25:11,061 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 21:25:11,072 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 21:25:11,089 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 21:25:11,093 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 21:25:11,095 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 21:25:11,095 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 21:25:11,111 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 21:25:11,115 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 21:25:11,118 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 21:25:11,119 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 21:25:11,121 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 21:25:11,124 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 21:25:11,130 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 21:25:11,131 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 21:25:11,131 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 21:25:11,131 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 21:25:11,135 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 21:25:11,135 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 21:25:11,136 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 21:25:11,136 - F2_Train - INFO - 数据验证通过
2025-07-23 21:25:11,136 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 21:25:11,137 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 21:25:11,160 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 25个
2025-07-23 21:25:17,833 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 21:25:17,842 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 21:25:18,326 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 21:25:18,334 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 21:25:18,341 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 21:25:18,361 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 21:25:18,394 - FeatureEngineer_f2 - INFO - 高级功率曲线特征提取完成
2025-07-23 21:25:18,399 - FeatureEngineer_f2 - INFO - 系统性偏差校正特征提取完成
2025-07-23 21:25:18,405 - FeatureEngineer_f2 - INFO - 高级组合特征提取完成
2025-07-23 21:25:18,887 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 257
2025-07-23 21:25:18,887 - F2_Train - INFO - 特征提取完成: (37821, 276)
2025-07-23 21:25:18,887 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 21:25:19,018 - FeatureEngineer_f2 - INFO - 相关性筛选: 257 -> 253
2025-07-23 21:26:06,490 - FeatureEngineer_f2 - INFO - 重要性筛选: 253 -> 127
2025-07-23 21:26:07,453 - FeatureEngineer_f2 - INFO - 高相关性筛选: 127 -> 57
2025-07-23 21:26:07,456 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 57
2025-07-23 21:26:07,464 - F2_Train - INFO - 特征选择完成: (37821, 59)
2025-07-23 21:26:07,482 - F2_Train - INFO - 最终训练数据: X(37821, 57), y(37821,)
2025-07-23 21:26:07,483 - F2_Train - INFO - 特征数量: 57
2025-07-23 21:26:07,483 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 21:26:07,484 - F2_Train - INFO - 开始超参数优化...
2025-07-23 21:26:07,484 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 21:32:20,841 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 43.9810
2025-07-23 21:32:20,841 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 33, 'learning_rate': 0.06835167275326788, 'feature_fraction': 0.8468738066791236, 'bagging_fraction': 0.8552324911284419, 'bagging_freq': 4, 'min_child_samples': 94, 'reg_alpha': 0.22499738905259564, 'reg_lambda': 0.14152867283753445}
2025-07-23 21:32:20,842 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 33, 'learning_rate': 0.06835167275326788, 'feature_fraction': 0.8468738066791236, 'bagging_fraction': 0.8552324911284419, 'bagging_freq': 4, 'min_child_samples': 94, 'reg_alpha': 0.22499738905259564, 'reg_lambda': 0.14152867283753445}
2025-07-23 21:32:20,842 - F2_Train - INFO - 开始交叉验证...
2025-07-23 21:32:20,843 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 21:32:21,072 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 44.8964
2025-07-23 21:32:21,321 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 51.6013
2025-07-23 21:32:21,605 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 32.3316
2025-07-23 21:32:21,975 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 56.0990
2025-07-23 21:32:22,438 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 37.7310
2025-07-23 21:32:22,439 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 21:32:22,439 - ModelTrainer_f2 - INFO -   rmse_mean: 44.5319
2025-07-23 21:32:22,439 - ModelTrainer_f2 - INFO -   rmse_std: 8.7064
2025-07-23 21:32:22,439 - ModelTrainer_f2 - INFO -   mae_mean: 33.2364
2025-07-23 21:32:22,439 - ModelTrainer_f2 - INFO -   mae_std: 8.6961
2025-07-23 21:32:22,440 - ModelTrainer_f2 - INFO -   r2_mean: 0.6407
2025-07-23 21:32:22,440 - ModelTrainer_f2 - INFO -   r2_std: 0.1231
2025-07-23 21:32:22,441 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(44.53186423373358), 'rmse_std': np.float64(8.70637436390832), 'mae_mean': np.float64(33.23643416888758), 'mae_std': np.float64(8.696114159711442), 'r2_mean': np.float64(0.6406790465378975), 'r2_std': np.float64(0.12308094779648023)}
2025-07-23 21:32:22,441 - F2_Train - INFO - 训练最终模型（高级策略）...
2025-07-23 21:32:22,441 - ModelTrainer_f2 - INFO - 开始高级模型训练...
2025-07-23 21:32:22,442 - ModelTrainer_f2 - INFO - 样本权重计算完成，权重范围: 0.90 - 1.17
2025-07-23 21:32:22,442 - ModelTrainer_f2 - INFO - 训练策略1: 全数据模型
2025-07-23 21:32:24,080 - ModelTrainer_f2 - INFO - 训练策略2: 高风速专门模型
2025-07-23 21:32:24,080 - ModelTrainer_f2 - ERROR - 高级模型训练失败: '10米风速（10m/s）'
2025-07-23 21:32:24,080 - F2_Train - ERROR - F2站点训练失败: '10米风速（10m/s）'
2025-07-23 21:32:24,093 - F2_Train - ERROR - Traceback (most recent call last):
  File "D:\anaconda3\envs\biaoge\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: '10米风速（10m/s）'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\train_f2.py", line 105, in main
    model = trainer.train_advanced_model(X, y, best_params)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\model_trainer_f2.py", line 301, in train_advanced_model
    high_wind_mask = X['10米风速（10m/s）'] >= 9
                     ~^^^^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\envs\biaoge\Lib\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\envs\biaoge\Lib\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: '10米风速（10m/s）'

2025-07-23 21:33:50,785 - F2_Train - INFO - ==================================================
2025-07-23 21:33:50,785 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 21:33:50,785 - F2_Train - INFO - ==================================================
2025-07-23 21:33:50,786 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 21:33:50,786 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 21:33:50,786 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 21:33:51,037 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 21:33:51,048 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 21:33:51,068 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 21:33:51,072 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 21:33:51,074 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 21:33:51,074 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 21:33:51,090 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 21:33:51,094 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 21:33:51,097 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 21:33:51,099 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 21:33:51,102 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 21:33:51,104 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 21:33:51,111 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 21:33:51,111 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 21:33:51,111 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 21:33:51,111 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 21:33:51,117 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 21:33:51,118 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 21:33:51,118 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 21:33:51,118 - F2_Train - INFO - 数据验证通过
2025-07-23 21:33:51,118 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 21:33:51,119 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 21:33:51,141 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 25个
2025-07-23 21:33:57,760 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 21:33:57,769 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 21:33:58,234 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 21:33:58,243 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 21:33:58,250 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 21:33:58,270 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 21:33:58,301 - FeatureEngineer_f2 - INFO - 高级功率曲线特征提取完成
2025-07-23 21:33:58,306 - FeatureEngineer_f2 - INFO - 系统性偏差校正特征提取完成
2025-07-23 21:33:58,311 - FeatureEngineer_f2 - INFO - 高级组合特征提取完成
2025-07-23 21:33:58,801 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 257
2025-07-23 21:33:58,801 - F2_Train - INFO - 特征提取完成: (37821, 276)
2025-07-23 21:33:58,801 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 21:33:58,928 - FeatureEngineer_f2 - INFO - 相关性筛选: 257 -> 253
2025-07-23 21:34:46,874 - FeatureEngineer_f2 - INFO - 重要性筛选: 253 -> 127
2025-07-23 21:34:47,831 - FeatureEngineer_f2 - INFO - 高相关性筛选: 127 -> 57
2025-07-23 21:34:47,833 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 57
2025-07-23 21:34:47,841 - F2_Train - INFO - 特征选择完成: (37821, 59)
2025-07-23 21:34:47,859 - F2_Train - INFO - 最终训练数据: X(37821, 57), y(37821,)
2025-07-23 21:34:47,859 - F2_Train - INFO - 特征数量: 57
2025-07-23 21:34:47,859 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 21:34:47,860 - F2_Train - INFO - 开始超参数优化...
2025-07-23 21:34:47,860 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 21:42:47,612 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 43.8339
2025-07-23 21:42:47,612 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 63, 'learning_rate': 0.06055334840378302, 'feature_fraction': 0.8658926902853479, 'bagging_fraction': 0.8412594190335468, 'bagging_freq': 3, 'min_child_samples': 98, 'reg_alpha': 0.3152095133638397, 'reg_lambda': 0.3293560851571317}
2025-07-23 21:42:47,613 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 63, 'learning_rate': 0.06055334840378302, 'feature_fraction': 0.8658926902853479, 'bagging_fraction': 0.8412594190335468, 'bagging_freq': 3, 'min_child_samples': 98, 'reg_alpha': 0.3152095133638397, 'reg_lambda': 0.3293560851571317}
2025-07-23 21:42:47,613 - F2_Train - INFO - 开始交叉验证...
2025-07-23 21:42:47,613 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 21:42:47,865 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 44.9519
2025-07-23 21:42:48,261 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 51.4210
2025-07-23 21:42:48,695 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 32.0569
2025-07-23 21:42:49,322 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 58.6660
2025-07-23 21:42:50,396 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 37.4159
2025-07-23 21:42:50,397 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 21:42:50,397 - ModelTrainer_f2 - INFO -   rmse_mean: 44.9023
2025-07-23 21:42:50,397 - ModelTrainer_f2 - INFO -   rmse_std: 9.5183
2025-07-23 21:42:50,397 - ModelTrainer_f2 - INFO -   mae_mean: 33.7750
2025-07-23 21:42:50,398 - ModelTrainer_f2 - INFO -   mae_std: 9.1015
2025-07-23 21:42:50,398 - ModelTrainer_f2 - INFO -   r2_mean: 0.6362
2025-07-23 21:42:50,398 - ModelTrainer_f2 - INFO -   r2_std: 0.1243
2025-07-23 21:42:50,399 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(44.90233013390868), 'rmse_std': np.float64(9.518251384821843), 'mae_mean': np.float64(33.77501282092503), 'mae_std': np.float64(9.10150932138147), 'r2_mean': np.float64(0.6361736637439787), 'r2_std': np.float64(0.12429933465252044)}
2025-07-23 21:42:50,399 - F2_Train - INFO - 训练最终模型（高级策略）...
2025-07-23 21:42:50,399 - ModelTrainer_f2 - INFO - 开始高级模型训练...
2025-07-23 21:42:50,401 - ModelTrainer_f2 - INFO - 样本权重计算完成，权重范围: 0.80 - 1.55
2025-07-23 21:42:50,401 - ModelTrainer_f2 - INFO - 训练策略1: 全数据模型
2025-07-23 21:42:52,214 - ModelTrainer_f2 - INFO - 训练策略2: 高风速专门模型
2025-07-23 21:42:52,505 - ModelTrainer_f2 - INFO - 训练策略3: 下午时段专门模型
2025-07-23 21:42:52,506 - ModelTrainer_f2 - ERROR - 高级模型训练失败: 'high_error_period'
2025-07-23 21:42:52,506 - F2_Train - ERROR - F2站点训练失败: 'high_error_period'
2025-07-23 21:42:52,512 - F2_Train - ERROR - Traceback (most recent call last):
  File "D:\anaconda3\envs\biaoge\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'high_error_period'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\train_f2.py", line 105, in main
    model = trainer.train_advanced_model(X, y, best_params)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\model_trainer_f2.py", line 325, in train_advanced_model
    afternoon_mask = X['high_error_period'] == 1
                     ~^^^^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\envs\biaoge\Lib\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\envs\biaoge\Lib\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'high_error_period'

2025-07-23 21:45:23,848 - F2_Train - INFO - ==================================================
2025-07-23 21:45:23,849 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 21:45:23,849 - F2_Train - INFO - ==================================================
2025-07-23 21:45:23,849 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 21:45:23,851 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 21:45:23,851 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 21:45:24,097 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 21:45:24,107 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 21:45:24,124 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 21:45:24,127 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 21:45:24,130 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 21:45:24,130 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 21:45:24,146 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 21:45:24,149 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 21:45:24,152 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 21:45:24,154 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 21:45:24,156 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 21:45:24,158 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 21:45:24,164 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 21:45:24,165 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 21:45:24,165 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 21:45:24,165 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 21:45:24,170 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 21:45:24,170 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 21:45:24,171 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 21:45:24,171 - F2_Train - INFO - 数据验证通过
2025-07-23 21:45:24,171 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 21:45:24,172 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 21:45:24,195 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 25个
2025-07-23 21:45:30,743 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 21:45:30,751 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 21:45:31,216 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 21:45:31,225 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 21:45:31,231 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 21:45:31,252 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 21:45:31,283 - FeatureEngineer_f2 - INFO - 高级功率曲线特征提取完成
2025-07-23 21:45:31,288 - FeatureEngineer_f2 - INFO - 系统性偏差校正特征提取完成
2025-07-23 21:45:31,294 - FeatureEngineer_f2 - INFO - 高级组合特征提取完成
2025-07-23 21:45:31,767 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 257
2025-07-23 21:45:31,767 - F2_Train - INFO - 特征提取完成: (37821, 276)
2025-07-23 21:45:31,767 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 21:45:31,896 - FeatureEngineer_f2 - INFO - 相关性筛选: 257 -> 253
2025-07-23 21:47:03,923 - F2_Train - INFO - ==================================================
2025-07-23 21:47:03,924 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 21:47:03,924 - F2_Train - INFO - ==================================================
2025-07-23 21:47:03,924 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 21:47:03,924 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 21:47:03,925 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 21:47:04,176 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 21:47:04,187 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 21:47:04,204 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 21:47:04,207 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 21:47:04,210 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 21:47:04,210 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 21:47:04,226 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 21:47:04,230 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 21:47:04,232 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 21:47:04,234 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 21:47:04,237 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 21:47:04,239 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 21:47:04,245 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 21:47:04,245 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 21:47:04,245 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 21:47:04,245 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 21:47:04,250 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 21:47:04,251 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 21:47:04,251 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 21:47:04,251 - F2_Train - INFO - 数据验证通过
2025-07-23 21:47:04,252 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 21:47:04,252 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 21:47:04,275 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 25个
2025-07-23 21:47:11,074 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 21:47:11,083 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 21:47:11,564 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 21:47:11,573 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 21:47:11,582 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 21:47:11,604 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 21:47:11,640 - FeatureEngineer_f2 - INFO - 高级功率曲线特征提取完成
2025-07-23 21:47:11,645 - FeatureEngineer_f2 - INFO - 系统性偏差校正特征提取完成
2025-07-23 21:47:11,654 - FeatureEngineer_f2 - INFO - 高级组合特征提取完成
2025-07-23 21:47:12,178 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 257
2025-07-23 21:47:12,179 - F2_Train - INFO - 特征提取完成: (37821, 276)
2025-07-23 21:47:12,179 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 21:47:12,316 - FeatureEngineer_f2 - INFO - 相关性筛选: 257 -> 253
2025-07-23 21:47:52,311 - FeatureEngineer_f2 - INFO - 重要性筛选: 253 -> 127
2025-07-23 21:47:53,255 - FeatureEngineer_f2 - INFO - 高相关性筛选: 127 -> 46
2025-07-23 21:47:53,257 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 46
2025-07-23 21:47:53,264 - F2_Train - INFO - 特征选择完成: (37821, 48)
2025-07-23 21:47:53,279 - F2_Train - INFO - 最终训练数据: X(37821, 46), y(37821,)
2025-07-23 21:47:53,279 - F2_Train - INFO - 特征数量: 46
2025-07-23 21:47:53,279 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 21:47:53,281 - F2_Train - INFO - 开始超参数优化...
2025-07-23 21:47:53,281 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 21:53:30,482 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 43.9709
2025-07-23 21:53:30,483 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 31, 'learning_rate': 0.07087349349882195, 'feature_fraction': 0.8515798292917239, 'bagging_fraction': 0.9324397384576585, 'bagging_freq': 2, 'min_child_samples': 98, 'reg_alpha': 0.11411147129922361, 'reg_lambda': 0.3238257837357559}
2025-07-23 21:53:30,484 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 31, 'learning_rate': 0.07087349349882195, 'feature_fraction': 0.8515798292917239, 'bagging_fraction': 0.9324397384576585, 'bagging_freq': 2, 'min_child_samples': 98, 'reg_alpha': 0.11411147129922361, 'reg_lambda': 0.3238257837357559}
2025-07-23 21:53:30,484 - F2_Train - INFO - 开始交叉验证...
2025-07-23 21:53:30,484 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 21:53:30,669 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 45.3271
2025-07-23 21:53:30,913 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 51.4891
2025-07-23 21:53:31,177 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 32.3806
2025-07-23 21:53:31,479 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 58.4552
2025-07-23 21:53:32,042 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 37.9356
2025-07-23 21:53:32,042 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 21:53:32,042 - ModelTrainer_f2 - INFO -   rmse_mean: 45.1175
2025-07-23 21:53:32,043 - ModelTrainer_f2 - INFO -   rmse_std: 9.2989
2025-07-23 21:53:32,043 - ModelTrainer_f2 - INFO -   mae_mean: 33.6457
2025-07-23 21:53:32,043 - ModelTrainer_f2 - INFO -   mae_std: 9.4225
2025-07-23 21:53:32,043 - ModelTrainer_f2 - INFO -   r2_mean: 0.6329
2025-07-23 21:53:32,044 - ModelTrainer_f2 - INFO -   r2_std: 0.1230
2025-07-23 21:53:32,044 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(45.117542554207816), 'rmse_std': np.float64(9.298856024896152), 'mae_mean': np.float64(33.64569707824558), 'mae_std': np.float64(9.422512344217886), 'r2_mean': np.float64(0.6329015062716854), 'r2_std': np.float64(0.12300494678695217)}
2025-07-23 21:53:32,045 - F2_Train - INFO - 训练最终模型（高级策略）...
2025-07-23 21:53:32,045 - ModelTrainer_f2 - INFO - 开始高级模型训练...
2025-07-23 21:53:32,046 - ModelTrainer_f2 - INFO - 样本权重计算完成，权重范围: 0.80 - 1.55
2025-07-23 21:53:32,046 - ModelTrainer_f2 - INFO - 训练策略1: 全数据模型
2025-07-23 21:53:33,653 - ModelTrainer_f2 - INFO - 训练策略2: 高风速专门模型
2025-07-23 21:53:33,823 - ModelTrainer_f2 - INFO - 训练策略3: 下午时段专门模型
2025-07-23 21:53:33,853 - ModelTrainer_f2 - INFO - 高级集成模型创建完成，包含3个子模型
2025-07-23 21:53:33,853 - F2_Train - INFO - 高级模型训练完成
2025-07-23 21:53:33,853 - F2_Train - INFO - 步骤4: 模型评估
2025-07-23 21:53:33,931 - ModelTrainer_f2 - INFO - 模型评估完成:
2025-07-23 21:53:33,931 - ModelTrainer_f2 - INFO -   RMSE: 41.8473
2025-07-23 21:53:33,932 - ModelTrainer_f2 - INFO -   MAE: 36.8075
2025-07-23 21:53:33,932 - ModelTrainer_f2 - INFO -   MAPE: 6087559678.1906
2025-07-23 21:53:33,932 - ModelTrainer_f2 - INFO -   R2: 0.7994
2025-07-23 21:53:33,932 - F2_Train - INFO - 模型评估完成:
2025-07-23 21:53:33,932 - F2_Train - INFO -   RMSE: 41.8473
2025-07-23 21:53:33,933 - F2_Train - INFO -   MAE: 36.8075
2025-07-23 21:53:33,933 - F2_Train - INFO -   MAPE: 6087559678.1906
2025-07-23 21:53:33,933 - F2_Train - INFO -   R2: 0.7994
2025-07-23 21:53:33,933 - F2_Train - INFO - 步骤5: 特征重要性分析
2025-07-23 21:53:33,933 - F2_Train - ERROR - F2站点训练失败: 'EnsembleModel' object has no attribute 'feature_importance'
2025-07-23 21:53:33,936 - F2_Train - ERROR - Traceback (most recent call last):
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\train_f2.py", line 117, in main
    importance_df = trainer.get_feature_importance(feature_cols)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\model_trainer_f2.py", line 452, in get_feature_importance
    importance = self.model.feature_importance(importance_type='gain')
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnsembleModel' object has no attribute 'feature_importance'

2025-07-23 21:54:25,738 - F2_Train - INFO - ==================================================
2025-07-23 21:54:25,738 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 21:54:25,739 - F2_Train - INFO - ==================================================
2025-07-23 21:54:25,739 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 21:54:25,739 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 21:54:25,740 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 21:54:25,990 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 21:54:26,001 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 21:54:26,023 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 21:54:26,027 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 21:54:26,029 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 21:54:26,030 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 21:54:26,051 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 21:54:26,053 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 21:54:26,056 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 21:54:26,058 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 21:54:26,060 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 21:54:26,063 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 21:54:26,069 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 21:54:26,069 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 21:54:26,069 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 21:54:26,069 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 21:54:26,073 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 21:54:26,075 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 21:54:26,075 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 21:54:26,075 - F2_Train - INFO - 数据验证通过
2025-07-23 21:54:26,075 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 21:54:26,076 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 21:54:26,100 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 25个
2025-07-23 21:54:32,738 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 21:54:32,745 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 21:54:33,206 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 21:54:33,214 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 21:54:33,220 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 21:54:33,240 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 21:54:33,272 - FeatureEngineer_f2 - INFO - 高级功率曲线特征提取完成
2025-07-23 21:54:33,277 - FeatureEngineer_f2 - INFO - 系统性偏差校正特征提取完成
2025-07-23 21:54:33,282 - FeatureEngineer_f2 - INFO - 高级组合特征提取完成
2025-07-23 21:54:33,773 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 257
2025-07-23 21:54:33,774 - F2_Train - INFO - 特征提取完成: (37821, 276)
2025-07-23 21:54:33,774 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 21:54:33,902 - FeatureEngineer_f2 - INFO - 相关性筛选: 257 -> 253
2025-07-23 21:55:10,435 - FeatureEngineer_f2 - INFO - 重要性筛选: 253 -> 127
2025-07-23 21:55:11,395 - FeatureEngineer_f2 - INFO - 高相关性筛选: 127 -> 46
2025-07-23 21:55:11,398 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 46
2025-07-23 21:55:11,404 - F2_Train - INFO - 特征选择完成: (37821, 48)
2025-07-23 21:55:11,419 - F2_Train - INFO - 最终训练数据: X(37821, 46), y(37821,)
2025-07-23 21:55:11,419 - F2_Train - INFO - 特征数量: 46
2025-07-23 21:55:11,420 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 21:55:11,421 - F2_Train - INFO - 开始超参数优化...
2025-07-23 21:55:11,421 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 22:00:10,631 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 43.3620
2025-07-23 22:00:10,631 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 36, 'learning_rate': 0.07031861486308945, 'feature_fraction': 0.7033347293912574, 'bagging_fraction': 0.7114701329406208, 'bagging_freq': 6, 'min_child_samples': 90, 'reg_alpha': 0.3602589884160528, 'reg_lambda': 0.17330245712413617}
2025-07-23 22:00:10,632 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 36, 'learning_rate': 0.07031861486308945, 'feature_fraction': 0.7033347293912574, 'bagging_fraction': 0.7114701329406208, 'bagging_freq': 6, 'min_child_samples': 90, 'reg_alpha': 0.3602589884160528, 'reg_lambda': 0.17330245712413617}
2025-07-23 22:00:10,632 - F2_Train - INFO - 开始交叉验证...
2025-07-23 22:00:10,633 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 22:00:10,820 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 43.6267
2025-07-23 22:00:11,082 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 52.1260
2025-07-23 22:00:11,358 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 32.2441
2025-07-23 22:00:11,701 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 58.6722
2025-07-23 22:00:12,120 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 38.1352
2025-07-23 22:00:12,121 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 22:00:12,121 - ModelTrainer_f2 - INFO -   rmse_mean: 44.9608
2025-07-23 22:00:12,121 - ModelTrainer_f2 - INFO -   rmse_std: 9.4808
2025-07-23 22:00:12,122 - ModelTrainer_f2 - INFO -   mae_mean: 33.3372
2025-07-23 22:00:12,122 - ModelTrainer_f2 - INFO -   mae_std: 9.4848
2025-07-23 22:00:12,122 - ModelTrainer_f2 - INFO -   r2_mean: 0.6342
2025-07-23 22:00:12,122 - ModelTrainer_f2 - INFO -   r2_std: 0.1292
2025-07-23 22:00:12,123 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(44.96082930773433), 'rmse_std': np.float64(9.480771080465592), 'mae_mean': np.float64(33.33723764874997), 'mae_std': np.float64(9.484778629082362), 'r2_mean': np.float64(0.6341912507145089), 'r2_std': np.float64(0.12917285430912293)}
2025-07-23 22:00:12,123 - F2_Train - INFO - 训练最终模型（高级策略）...
2025-07-23 22:00:12,124 - ModelTrainer_f2 - INFO - 开始高级模型训练...
2025-07-23 22:00:12,125 - ModelTrainer_f2 - INFO - 样本权重计算完成，权重范围: 0.80 - 1.55
2025-07-23 22:00:12,125 - ModelTrainer_f2 - INFO - 训练策略1: 全数据模型
2025-07-23 22:00:13,684 - ModelTrainer_f2 - INFO - 训练策略2: 高风速专门模型
2025-07-23 22:00:13,839 - ModelTrainer_f2 - INFO - 训练策略3: 下午时段专门模型
2025-07-23 22:00:13,860 - ModelTrainer_f2 - INFO - 高级集成模型创建完成，包含3个子模型
2025-07-23 22:00:13,861 - F2_Train - INFO - 高级模型训练完成
2025-07-23 22:00:13,861 - F2_Train - INFO - 步骤4: 模型评估
2025-07-23 22:00:13,944 - ModelTrainer_f2 - INFO - 模型评估完成:
2025-07-23 22:00:13,944 - ModelTrainer_f2 - INFO -   RMSE: 41.4511
2025-07-23 22:00:13,944 - ModelTrainer_f2 - INFO -   MAE: 36.4420
2025-07-23 22:00:13,944 - ModelTrainer_f2 - INFO -   MAPE: 6160192737.2846
2025-07-23 22:00:13,945 - ModelTrainer_f2 - INFO -   R2: 0.8031
2025-07-23 22:00:13,945 - F2_Train - INFO - 模型评估完成:
2025-07-23 22:00:13,945 - F2_Train - INFO -   RMSE: 41.4511
2025-07-23 22:00:13,945 - F2_Train - INFO -   MAE: 36.4420
2025-07-23 22:00:13,945 - F2_Train - INFO -   MAPE: 6160192737.2846
2025-07-23 22:00:13,945 - F2_Train - INFO -   R2: 0.8031
2025-07-23 22:00:13,945 - F2_Train - INFO - 步骤5: 特征重要性分析
2025-07-23 22:00:13,945 - F2_Train - ERROR - F2站点训练失败: 'LGBMRegressor' object has no attribute 'feature_importance'
2025-07-23 22:00:13,950 - F2_Train - ERROR - Traceback (most recent call last):
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\train_f2.py", line 117, in main
    importance_df = trainer.get_feature_importance(feature_cols)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\sites\f2\model_trainer_f2.py", line 456, in get_feature_importance
    importance = main_model.feature_importance(importance_type='gain')
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'LGBMRegressor' object has no attribute 'feature_importance'. Did you mean: 'feature_importances_'?

2025-07-23 22:00:56,640 - F2_Train - INFO - ==================================================
2025-07-23 22:00:56,641 - F2_Train - INFO - 开始F2站点训练流程
2025-07-23 22:00:56,641 - F2_Train - INFO - ==================================================
2025-07-23 22:00:56,641 - F2_Train - INFO - 站点信息: f2风电场 (280MW)
2025-07-23 22:00:56,641 - F2_Train - INFO - 步骤1: 数据加载和处理
2025-07-23 22:00:56,642 - DataProcessor_f2 - INFO - 开始加载数据: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\data\train\训练集_风电预测_气象变量及实际功率数据_截止1月.csv
2025-07-23 22:00:56,899 - DataProcessor_f2 - INFO - 使用GBK编码读取数据
2025-07-23 22:00:56,909 - DataProcessor_f2 - INFO - F2站点数据加载完成，共37824行
2025-07-23 22:00:56,927 - DataProcessor_f2 - INFO - 时间列处理完成
2025-07-23 22:00:56,930 - DataProcessor_f2 - INFO - 筛选有效训练数据后，共37821行
2025-07-23 22:00:56,932 - F2_Train - INFO - 训练数据加载完成: (37821, 13)
2025-07-23 22:00:56,932 - DataProcessor_f2 - INFO - 开始数据清洗
2025-07-23 22:00:56,949 - DataProcessor_f2 - INFO - 缺失值处理: 0 -> 0
2025-07-23 22:00:56,952 - DataProcessor_f2 - INFO - 10米风速（10m/s）异常值处理: 380个
2025-07-23 22:00:56,954 - DataProcessor_f2 - INFO - 温度（K）异常值处理: 380个
2025-07-23 22:00:56,957 - DataProcessor_f2 - INFO - 气压(Pa）异常值处理: 380个
2025-07-23 22:00:56,960 - DataProcessor_f2 - INFO - 相对湿度（%）异常值处理: 380个
2025-07-23 22:00:56,962 - DataProcessor_f2 - INFO - 100m风速（100m/s）异常值处理: 380个
2025-07-23 22:00:56,969 - DataProcessor_f2 - INFO - 数据平滑完成，窗口大小: 5
2025-07-23 22:00:56,969 - DataProcessor_f2 - INFO - 数据清洗完成，最终数据量: 37821行
2025-07-23 22:00:56,969 - F2_Train - INFO - 数据清洗完成: (37821, 18)
2025-07-23 22:00:56,969 - DataProcessor_f2 - INFO - 开始数据质量验证
2025-07-23 22:00:56,977 - DataProcessor_f2 - WARNING - 时间间隔不规律的记录: 37820个
2025-07-23 22:00:56,978 - DataProcessor_f2 - WARNING - 出力超出范围的记录: 26716个
2025-07-23 22:00:56,979 - DataProcessor_f2 - INFO - 数据质量验证通过
2025-07-23 22:00:56,979 - F2_Train - INFO - 数据验证通过
2025-07-23 22:00:56,979 - F2_Train - INFO - 步骤2: 特征工程
2025-07-23 22:00:56,980 - FeatureEngineer_f2 - INFO - 开始F2站点特征提取
2025-07-23 22:00:57,006 - FeatureEngineer_f2 - INFO - 时间特征提取完成: 25个
2025-07-23 22:01:03,773 - FeatureEngineer_f2 - INFO - 风速敏感性特征提取完成
2025-07-23 22:01:03,780 - FeatureEngineer_f2 - INFO - 滞后特征提取完成: 6个滞后期
2025-07-23 22:01:04,254 - FeatureEngineer_f2 - INFO - 滚动统计特征提取完成
2025-07-23 22:01:04,263 - FeatureEngineer_f2 - INFO - 交互特征提取完成
2025-07-23 22:01:04,270 - FeatureEngineer_f2 - INFO - 技术指标特征提取完成
2025-07-23 22:01:04,291 - FeatureEngineer_f2 - INFO - 异常检测特征提取完成
2025-07-23 22:01:04,322 - FeatureEngineer_f2 - INFO - 高级功率曲线特征提取完成
2025-07-23 22:01:04,327 - FeatureEngineer_f2 - INFO - 系统性偏差校正特征提取完成
2025-07-23 22:01:04,334 - FeatureEngineer_f2 - INFO - 高级组合特征提取完成
2025-07-23 22:01:04,821 - FeatureEngineer_f2 - INFO - F2站点特征提取完成，特征数: 257
2025-07-23 22:01:04,822 - F2_Train - INFO - 特征提取完成: (37821, 276)
2025-07-23 22:01:04,822 - FeatureEngineer_f2 - INFO - 开始特征选择
2025-07-23 22:01:04,946 - FeatureEngineer_f2 - INFO - 相关性筛选: 257 -> 253
2025-07-23 22:01:41,718 - FeatureEngineer_f2 - INFO - 重要性筛选: 253 -> 127
2025-07-23 22:01:42,683 - FeatureEngineer_f2 - INFO - 高相关性筛选: 127 -> 46
2025-07-23 22:01:42,685 - FeatureEngineer_f2 - INFO - 特征选择完成，最终特征数: 46
2025-07-23 22:01:42,692 - F2_Train - INFO - 特征选择完成: (37821, 48)
2025-07-23 22:01:42,707 - F2_Train - INFO - 最终训练数据: X(37821, 46), y(37821,)
2025-07-23 22:01:42,708 - F2_Train - INFO - 特征数量: 46
2025-07-23 22:01:42,708 - F2_Train - INFO - 步骤3: 模型训练
2025-07-23 22:01:42,709 - F2_Train - INFO - 开始超参数优化...
2025-07-23 22:01:42,709 - ModelTrainer_f2 - INFO - 开始F2站点超参数优化
2025-07-23 22:07:39,599 - ModelTrainer_f2 - INFO - 超参数优化完成，最佳分数: 43.7551
2025-07-23 22:07:39,600 - ModelTrainer_f2 - INFO - 最佳参数: {'num_leaves': 40, 'learning_rate': 0.06615087425551408, 'feature_fraction': 0.8679658899367809, 'bagging_fraction': 0.7889828078333683, 'bagging_freq': 3, 'min_child_samples': 75, 'reg_alpha': 0.13180703781971592, 'reg_lambda': 0.4115743321781202}
2025-07-23 22:07:39,601 - F2_Train - INFO - 超参数优化完成: {'num_leaves': 40, 'learning_rate': 0.06615087425551408, 'feature_fraction': 0.8679658899367809, 'bagging_fraction': 0.7889828078333683, 'bagging_freq': 3, 'min_child_samples': 75, 'reg_alpha': 0.13180703781971592, 'reg_lambda': 0.4115743321781202}
2025-07-23 22:07:39,601 - F2_Train - INFO - 开始交叉验证...
2025-07-23 22:07:39,601 - ModelTrainer_f2 - INFO - 开始交叉验证
2025-07-23 22:07:39,818 - ModelTrainer_f2 - INFO - Fold 1 - RMSE: 45.7628
2025-07-23 22:07:40,101 - ModelTrainer_f2 - INFO - Fold 2 - RMSE: 51.4221
2025-07-23 22:07:40,396 - ModelTrainer_f2 - INFO - Fold 3 - RMSE: 32.5337
2025-07-23 22:07:40,782 - ModelTrainer_f2 - INFO - Fold 4 - RMSE: 59.3628
2025-07-23 22:07:41,197 - ModelTrainer_f2 - INFO - Fold 5 - RMSE: 38.8709
2025-07-23 22:07:41,198 - ModelTrainer_f2 - INFO - 交叉验证完成:
2025-07-23 22:07:41,198 - ModelTrainer_f2 - INFO -   rmse_mean: 45.5905
2025-07-23 22:07:41,198 - ModelTrainer_f2 - INFO -   rmse_std: 9.3739
2025-07-23 22:07:41,198 - ModelTrainer_f2 - INFO -   mae_mean: 34.2021
2025-07-23 22:07:41,199 - ModelTrainer_f2 - INFO -   mae_std: 9.2316
2025-07-23 22:07:41,199 - ModelTrainer_f2 - INFO -   r2_mean: 0.6269
2025-07-23 22:07:41,199 - ModelTrainer_f2 - INFO -   r2_std: 0.1205
2025-07-23 22:07:41,200 - F2_Train - INFO - 交叉验证完成: {'rmse_mean': np.float64(45.59046125212289), 'rmse_std': np.float64(9.373874777547014), 'mae_mean': np.float64(34.2021089727575), 'mae_std': np.float64(9.231567103601227), 'r2_mean': np.float64(0.626912432790774), 'r2_std': np.float64(0.120519012209066)}
2025-07-23 22:07:41,200 - F2_Train - INFO - 训练最终模型（高级策略）...
2025-07-23 22:07:41,200 - ModelTrainer_f2 - INFO - 开始高级模型训练...
2025-07-23 22:07:41,202 - ModelTrainer_f2 - INFO - 样本权重计算完成，权重范围: 0.80 - 1.55
2025-07-23 22:07:41,202 - ModelTrainer_f2 - INFO - 训练策略1: 全数据模型
2025-07-23 22:07:42,842 - ModelTrainer_f2 - INFO - 训练策略2: 高风速专门模型
2025-07-23 22:07:43,043 - ModelTrainer_f2 - INFO - 训练策略3: 下午时段专门模型
2025-07-23 22:07:43,070 - ModelTrainer_f2 - INFO - 高级集成模型创建完成，包含3个子模型
2025-07-23 22:07:43,070 - F2_Train - INFO - 高级模型训练完成
2025-07-23 22:07:43,070 - F2_Train - INFO - 步骤4: 模型评估
2025-07-23 22:07:43,154 - ModelTrainer_f2 - INFO - 模型评估完成:
2025-07-23 22:07:43,154 - ModelTrainer_f2 - INFO -   RMSE: 40.9280
2025-07-23 22:07:43,154 - ModelTrainer_f2 - INFO -   MAE: 35.9357
2025-07-23 22:07:43,154 - ModelTrainer_f2 - INFO -   MAPE: 6110776720.0294
2025-07-23 22:07:43,154 - ModelTrainer_f2 - INFO -   R2: 0.8081
2025-07-23 22:07:43,154 - F2_Train - INFO - 模型评估完成:
2025-07-23 22:07:43,154 - F2_Train - INFO -   RMSE: 40.9280
2025-07-23 22:07:43,154 - F2_Train - INFO -   MAE: 35.9357
2025-07-23 22:07:43,156 - F2_Train - INFO -   MAPE: 6110776720.0294
2025-07-23 22:07:43,156 - F2_Train - INFO -   R2: 0.8081
2025-07-23 22:07:43,156 - F2_Train - INFO - 步骤5: 特征重要性分析
2025-07-23 22:07:43,157 - F2_Train - INFO - Top 10 重要特征:
2025-07-23 22:07:43,157 - F2_Train - INFO -   day_of_year_sin: 267.00
2025-07-23 22:07:43,157 - F2_Train - INFO -   day_of_year: 232.00
2025-07-23 22:07:43,158 - F2_Train - INFO -   10米风速（10m/s）_lag_3h: 195.00
2025-07-23 22:07:43,158 - F2_Train - INFO -   wind_speed_ratio_100_10: 187.00
2025-07-23 22:07:43,158 - F2_Train - INFO -   气压(Pa）_lag_1h: 181.00
2025-07-23 22:07:43,158 - F2_Train - INFO -   温度（K）_lag_12h: 151.00
2025-07-23 22:07:43,158 - F2_Train - INFO -   day_of_week: 142.00
2025-07-23 22:07:43,159 - F2_Train - INFO -   dew_point_approx: 137.00
2025-07-23 22:07:43,159 - F2_Train - INFO -   10米风速（10m/s）_std_48h: 134.00
2025-07-23 22:07:43,159 - F2_Train - INFO -   10米风速（10m/s）_lag_48h: 125.00
2025-07-23 22:07:43,162 - F2_Train - INFO - 特征重要性保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_feature_importance.csv
2025-07-23 22:07:43,162 - F2_Train - INFO - 步骤6: 保存模型
2025-07-23 22:07:43,185 - ModelTrainer_f2 - INFO - 模型保存成功: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_lgb_model.pkl
2025-07-23 22:07:43,186 - F2_Train - INFO - 特征名称保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_feature_names.txt
2025-07-23 22:07:43,187 - F2_Train - INFO - 训练报告保存: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2\f2_training_report.txt
2025-07-23 22:07:43,187 - F2_Train - INFO - ==================================================
2025-07-23 22:07:43,187 - F2_Train - INFO - F2站点训练流程完成！耗时: 406.55秒
2025-07-23 22:07:43,188 - F2_Train - INFO - 模型保存路径: D:\handj\Documents\PycharmProjects\表格类\2025浙江能源预测\models\models_f2
2025-07-23 22:07:43,188 - F2_Train - INFO - ==================================================
