"""
风电预测API服务
提供HTTP API接口进行风电出力预测
"""

import sys
import json
import time
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from flask import Flask, request, jsonify
import traceback
import logging
import io

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入各站点预测模块
from sites.f1.data_processor_f1 import DataProcessorF1
from sites.f1.feature_engineer_f1 import FeatureEngineerF1
from sites.f1.predictor_f1 import PredictorF1

from sites.f2.data_processor_f2 import DataProcessorF2
from sites.f2.feature_engineer_f2 import FeatureEngineerF2
from sites.f2.predictor_f2 import PredictorF2

from sites.f3.data_processor_f3 import DataProcessorF3
from sites.f3.feature_engineer_f3 import FeatureEngineerF3
from sites.f3.predictor_f3 import PredictorF3

from sites.f4.data_processor_f4 import DataProcessorF4
from sites.f4.feature_engineer_f4 import FeatureEngineerF4
from sites.f4.predictor_f4 import PredictorF4

from sites.f5.data_processor_f5 import DataProcessorF5
from sites.f5.feature_engineer_f5 import FeatureEngineerF5
from sites.f5.predictor_f5 import PredictorF5

# 创建Flask应用
app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SitePredictionPipeline:
    """站点预测流水线"""

    def __init__(self, site_id):
        self.site_id = site_id
        self.data_processor = None
        self.feature_engineer = None
        self.predictor = None
        self.feature_names = None
        self.load_components()

    def load_components(self):
        """加载站点组件"""
        components = {
            'f1': (DataProcessorF1, FeatureEngineerF1, PredictorF1),
            'f2': (DataProcessorF2, FeatureEngineerF2, PredictorF2),
            'f3': (DataProcessorF3, FeatureEngineerF3, PredictorF3),
            'f4': (DataProcessorF4, FeatureEngineerF4, PredictorF4),
            'f5': (DataProcessorF5, FeatureEngineerF5, PredictorF5)
        }

        if self.site_id not in components:
            raise ValueError(f"不支持的站点: {self.site_id}")

        processor_class, engineer_class, predictor_class = components[self.site_id]

        # 初始化组件
        self.data_processor = processor_class()
        self.feature_engineer = engineer_class()
        self.predictor = predictor_class()

        # 加载模型
        self.predictor.load_model()

        # 加载特征名称
        self.load_feature_names()

    def load_feature_names(self):
        """加载特征名称"""
        model_dir = Path(f'models/models_{self.site_id}')
        feature_names_path = model_dir / f'{self.site_id}_feature_names.txt'

        if feature_names_path.exists():
            with open(feature_names_path, 'r', encoding='utf-8') as f:
                self.feature_names = [line.strip() for line in f.readlines()]

    def predict(self, input_df):
        """执行预测"""
        # 数据处理：先处理时间列，再清洗数据
        df_copy = input_df.copy()

        # 处理时间列
        df_copy = self.data_processor._process_datetime(df_copy)

        # 数据清洗
        processed_df = self.data_processor.clean_data(df_copy)

        # 数据验证
        if not self.data_processor.validate_data(processed_df):
            raise ValueError(f"{self.site_id}站点数据验证失败")

        # 特征工程
        features_df = self.feature_engineer.extract_features(processed_df)

        # 准备预测数据
        if self.feature_names:
            # 确保所有特征都存在
            available_features = [f for f in self.feature_names if f in features_df.columns]
            if len(available_features) != len(self.feature_names):
                missing_features = set(self.feature_names) - set(available_features)
                logger.warning(f"{self.site_id}站点缺少特征: {missing_features}")
            prediction_data = features_df[available_features]
        else:
            prediction_data = features_df

        # 执行预测
        predictions = self.predictor.predict(prediction_data)

        # 格式化结果 - 确保时间列是datetime格式
        timestamps = processed_df['时间']  # 使用处理后的时间列
        if not pd.api.types.is_datetime64_any_dtype(timestamps):
            timestamps = pd.to_datetime(timestamps)

        result_df = self.predictor.format_predictions(
            predictions, timestamps
        )

        return result_df

class WindPowerPredictionAPI:
    """风电预测API服务类"""

    def __init__(self):
        """初始化API服务"""
        self.pipelines = {}
        self.site_info = {
            'f1': {'name': 'f1风电场', 'capacity': 48},
            'f2': {'name': 'f2风电场', 'capacity': 280},
            'f3': {'name': 'f3风电场', 'capacity': 48},
            'f4': {'name': 'f4风电场', 'capacity': 88},
            'f5': {'name': 'f5风电场', 'capacity': 48}
        }
        self.load_pipelines()

    def load_pipelines(self):
        """加载所有站点的预测流水线"""
        logger.info("正在加载所有站点的预测流水线...")

        for site_id in self.site_info.keys():
            try:
                self.pipelines[site_id] = SitePredictionPipeline(site_id)
                logger.info(f"✅ {site_id.upper()}站点流水线加载成功")
            except Exception as e:
                logger.error(f"❌ {site_id.upper()}站点流水线加载失败: {str(e)}")
                raise

        logger.info(f"所有站点流水线加载完成，共{len(self.pipelines)}个站点")
    
    def validate_input_data(self, df):
        """验证输入数据格式"""
        required_columns = ['站点编号', '时间', '10米风速（10m/s）', '温度（K）', '气压(Pa）', '相对湿度（%）']
        
        # 检查必需列
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必需的列: {missing_columns}")
        
        # 检查站点编号
        valid_sites = set(self.site_info.keys())
        input_sites = set(df['站点编号'].unique())
        invalid_sites = input_sites - valid_sites
        if invalid_sites:
            raise ValueError(f"无效的站点编号: {invalid_sites}")

        # 检查数据量
        if len(df) == 0:
            raise ValueError("输入数据为空")

        logger.info(f"输入数据验证通过: {len(df)}条记录，站点: {sorted(input_sites)}")
        return True

    def predict_all_sites(self, input_df):
        """预测所有站点的出力"""
        logger.info("开始预测所有站点...")

        results = []
        prediction_stats = {}

        # 获取输入数据中的站点
        input_sites = sorted(input_df['站点编号'].unique())

        for site_id in input_sites:
            if site_id not in self.pipelines:
                logger.warning(f"跳过未知站点: {site_id}")
                continue

            try:
                logger.info(f"正在预测{site_id.upper()}站点...")
                start_time = time.time()

                # 提取站点数据
                site_data = input_df[input_df['站点编号'] == site_id].copy()

                # 调用站点预测流水线
                pipeline = self.pipelines[site_id]
                predictions = pipeline.predict(site_data)

                # 提取功率预测值，处理缺失值
                power_values = predictions['出力(MW)'].tolist()
                # 将NaN值转换为null
                power_values = [None if pd.isna(val) else float(val) for val in power_values]

                # 添加到结果列表
                results.append({
                    'site': site_id,
                    'power': power_values
                })

                duration = time.time() - start_time
                prediction_stats[site_id] = {
                    'records': len(predictions),
                    'duration': duration,
                    'mean_power': predictions['出力(MW)'].mean(),
                    'max_power': predictions['出力(MW)'].max(),
                    'min_power': predictions['出力(MW)'].min()
                }

                logger.info(f"✅ {site_id.upper()}站点预测完成: {len(predictions)}条记录，耗时{duration:.2f}秒")

            except Exception as e:
                logger.error(f"❌ {site_id.upper()}站点预测失败: {str(e)}")
                raise

        # 返回结果
        if results:
            logger.info(f"所有站点预测完成，共{len(results)}个站点")
            return results, prediction_stats
        else:
            raise ValueError("没有成功预测任何站点")
    
    def save_predictions(self, predictions_df):
        """保存预测结果到submission.csv"""
        submissions_dir = Path('submissions')
        submissions_dir.mkdir(exist_ok=True)
        
        output_file = submissions_dir / 'submission.csv'
        predictions_df.to_csv(output_file, index=False, encoding='utf-8')
        
        logger.info(f"预测结果已保存到: {output_file}")
        return output_file

# 创建API服务实例
api_service = WindPowerPredictionAPI()

@app.route('/predict', methods=['POST'])
def predict():
    """预测接口"""
    try:
        start_time = time.time()
        logger.info("收到预测请求")

        # 检查是否包含文件
        if 'file' not in request.files:
            return jsonify({
                'status': 0,
                'results': []
            }), 400

        file = request.files['file']

        # 检查文件名
        if file.filename == '':
            return jsonify({
                'status': 0,
                'results': []
            }), 400

        # 检查文件类型
        if not file.filename.lower().endswith('.csv'):
            return jsonify({
                'status': 0,
                'results': []
            }), 400

        # 读取CSV文件
        try:
            # 读取文件内容
            file_content = file.read()

            # 将字节内容转换为字符串
            csv_string = file_content.decode('utf-8')

            # 使用StringIO创建文件对象
            csv_io = io.StringIO(csv_string)

            # 读取CSV数据
            input_df = pd.read_csv(csv_io)

        except Exception as e:
            logger.error(f"读取CSV文件失败: {str(e)}")
            return jsonify({
                'status': 0,
                'results': []
            }), 400

        # 验证输入数据
        try:
            api_service.validate_input_data(input_df)
        except Exception as e:
            logger.error(f"输入数据验证失败: {str(e)}")
            return jsonify({
                'status': 0,
                'results': []
            }), 400

        # 执行预测
        try:
            results, prediction_stats = api_service.predict_all_sites(input_df)
        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            return jsonify({
                'status': 0,
                'results': []
            }), 500

        total_duration = time.time() - start_time

        # 返回成功响应
        response = {
            'status': 1,
            'results': results
        }

        logger.info(f"预测请求处理完成，耗时{total_duration:.2f}秒")
        return jsonify(response)

    except Exception as e:
        error_msg = str(e)
        logger.error(f"预测请求处理失败: {error_msg}")
        logger.error(traceback.format_exc())

        return jsonify({
            'status': 0,
            'results': []
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': 'wind_power_prediction_api',
        'loaded_sites': list(api_service.pipelines.keys()),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/info', methods=['GET'])
def service_info():
    """服务信息接口"""
    return jsonify({
        'service': 'wind_power_prediction_api',
        'version': '2.0.0',
        'description': '风电出力预测API服务',
        'sites': api_service.site_info,
        'endpoints': {
            '/predict': 'POST - 风电出力预测 (接收CSV文件)',
            '/health': 'GET - 健康检查',
            '/info': 'GET - 服务信息'
        },
        'input_format': {
            'method': 'POST',
            'content_type': 'multipart/form-data',
            'parameters': {
                'file': {
                    'type': 'file',
                    'format': 'CSV',
                    'required': True,
                    'description': '包含天气变量的CSV文件，格式与测试集A_风电预测_气象变量.csv相同'
                }
            }
        },
        'output_format': {
            'status': 'int (0失败，1成功)',
            'results': 'list (包含site和power字段的结果列表)'
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    try:
        # 启动服务
        host = '0.0.0.0'
        port = 8888
        
        logger.info(f"启动风电预测API服务...")
        logger.info(f"服务地址: http://{host}:{port}")
        logger.info(f"预测接口: http://{host}:{port}/predict")
        logger.info(f"健康检查: http://{host}:{port}/health")
        logger.info(f"服务信息: http://{host}:{port}/info")
        
        app.run(host=host, port=port, debug=False, threaded=True)
        
    except Exception as e:
        logger.error(f"API服务启动失败: {str(e)}")
        traceback.print_exc()
