"""
F4站点预测入口脚本
调用sites/f4/predict_f4.py进行F4站点预测
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入F4站点预测模块
from sites.f4.predict_f4 import main as predict_f4_main

if __name__ == "__main__":
    print("开始F4站点预测...")
    success = predict_f4_main()
    
    if success:
        print("F4站点预测成功完成！")
    else:
        print("F4站点预测失败！")
        sys.exit(1)
