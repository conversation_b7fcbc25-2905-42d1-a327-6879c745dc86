# 风电功率预测系统依赖包
# Python 3.8+

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 机器学习
scikit-learn>=1.2.0
lightgbm>=3.3.0

# 超参数优化
optuna>=3.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 工具库
joblib>=1.2.0
tqdm>=4.64.0

# Web框架 (API服务)
flask>=2.0.0
requests>=2.28.0

# 配置文件处理
pyyaml>=6.0

# 日志和调试
loguru>=0.6.0

# 数据验证
pandera>=0.13.0

# 时间处理
python-dateutil>=2.8.0

# 数值计算优化
numba>=0.56.0

# 内存优化
psutil>=5.9.0

# 文件处理
openpyxl>=3.0.0
xlrd>=2.0.0

# 统计分析
scipy>=1.9.0
statsmodels>=0.13.0

# 进度条
rich>=12.0.0

# 类型检查
typing-extensions>=4.0.0

# 测试框架
pytest>=7.0.0
pytest-cov>=4.0.0

# 代码质量
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0

# 文档生成
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
