"""
F1站点配置文件
48MW小型风电场配置
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from shared.common_constants import *

# F1站点基本信息
F1_CONFIG = {
    'site_info': {
        'site_id': 'f1',
        'name': 'f1风电场',
        'capacity_mw': 48,
        'type': 'small'
    },
    
    # 数据路径配置
    'data_paths': {
        'train_data': DATA_PATHS['train'],
        'test_data': DATA_PATHS['test'],
        'model_dir': PATHS['models'] / 'models_f1',
        'output_dir': PATHS['submissions'],
        'log_dir': PATHS['logs']
    },
    
    # 特征工程配置（小型站点特定）
    'feature_config': {
        'time_features': TIME_FEATURES['extended'],
        'lag_features': LAG_FEATURES['small_site'],
        'rolling_windows': ROLLING_WINDOWS['small_site'],
        'weather_features': WEATHER_FEATURES,
        
        # 小型站点特定特征
        'wind_sensitivity_features': {
            'wind_change_windows': [1, 3, 6],      # 风速变化窗口
            'volatility_windows': [3, 6, 12],      # 波动性窗口
            'acceleration_windows': [1, 2, 3]       # 加速度窗口
        },
        
        # 特征选择配置
        'feature_selection': {
            'correlation_threshold': 0.95,
            'importance_threshold': 0.001,
            'max_features': 80  # 小型站点特征数限制
        }
    },
    
    # 模型配置（小型站点优化）
    'model_config': {
        'lgb_base_params': {
            **MODEL_CONFIG['lgb_base_params'],
            'max_depth': 6,  # 小型站点适中深度
            'min_data_in_leaf': 20,
            'lambda_l1': 0.1,
            'lambda_l2': 0.1
        },
        
        'optuna_config': {
            'n_trials': MODEL_CONFIG['optuna_config']['n_trials']['small_site'],
            'timeout': 1800,  # 30分钟
            'cv_folds': 5,
            'scoring': 'neg_root_mean_squared_error'
        },
        
        'hyperparameter_space': HYPERPARAMETER_SPACE['small_site']
    },
    
    # 数据处理配置
    'data_processing': {
        **DATA_PROCESSING_CONFIG,
        'outlier_quantiles': (0.01, 0.99),  # 小型站点更严格的异常值处理
        'missing_strategy': 'interpolate',
        'smoothing_window': 3  # 小型站点数据平滑窗口
    },
    
    # 预测配置
    'prediction_config': {
        'output_columns': OUTPUT_CONFIG['columns'],
        'time_format': OUTPUT_CONFIG['time_format'],
        'capacity_constraint': True,  # 启用容量约束
        'min_output': 0.0,
        'max_output': 48.0,  # F1站点装机容量
        'post_processing': {
            'smooth_predictions': True,
            'smooth_window': 3
        }
    },
    
    # 错误处理配置
    'error_handling': {
        **ERROR_HANDLING_CONFIG,
        'site_specific_fallbacks': {
            'data_missing_threshold': 0.05,  # 小型站点数据缺失阈值
            'model_performance_threshold': 0.8,  # 模型性能阈值
            'prediction_range_check': True
        }
    },
    
    # 日志配置
    'logging': {
        **LOGGING_CONFIG,
        'log_file': f'logs/f1_site.log',
        'detailed_logging': True
    },
    
    # 验证配置
    'validation': {
        'train_test_split': 0.2,
        'time_series_split': True,
        'validation_metrics': EVALUATION_METRICS,
        'early_stopping_rounds': 100
    }
}

# 导出配置
def get_f1_config():
    """获取F1站点配置"""
    # 确保模型目录存在
    F1_CONFIG['data_paths']['model_dir'].mkdir(exist_ok=True)
    return F1_CONFIG

if __name__ == "__main__":
    # 测试配置
    config = get_f1_config()
    print("F1站点配置加载成功")
    print(f"站点ID: {config['site_info']['site_id']}")
    print(f"装机容量: {config['site_info']['capacity_mw']}MW")
    print(f"模型目录: {config['data_paths']['model_dir']}")
